/* Global Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  direction: rtl;
  color: #2c3e50;
}

.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Header Styles */
.app-header {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  padding: 1rem 2rem;
  text-align: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.header-content h1 {
  color: white;
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.header-content p {
  color: rgba(255, 255, 255, 0.95);
  font-size: 1.1rem;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

/* Navigation Styles */
.app-nav {
  background: rgba(255, 255, 255, 0.05);
  padding: 1rem 2rem;
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.nav-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
  font-weight: 500;
}

.nav-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.nav-btn.active {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Main Content Styles */
.app-main {
  flex: 1;
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

/* Card Styles */
.card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.card h2 {
  color: #2c3e50;
  margin-bottom: 1rem;
  font-size: 1.8rem;
  font-weight: 600;
}

.card h3 {
  color: #34495e;
  margin-bottom: 0.75rem;
  font-size: 1.3rem;
  font-weight: 500;
}

.card p {
  color: #5a6c7d;
  line-height: 1.6;
}

/* Form Styles */
.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: #2c3e50;
  font-weight: 600;
}

.form-input {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #ddd;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
  color: #2c3e50;
  background-color: #ffffff;
}

.form-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-textarea {
  min-height: 100px;
  resize: vertical;
}

/* Button Styles */
.btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 0.75rem 2rem;
  border-radius: 25px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 500;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.btn-secondary {
  background: #6c757d;
}

.btn-danger {
  background: #dc3545;
}

.btn-success {
  background: #28a745;
}

/* Loading Spinner */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Results Styles */
.results {
  margin-top: 2rem;
}

.vulnerability-item {
  background: #ffffff;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.vulnerability-item h4 {
  color: #2c3e50;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.vulnerability-item p {
  color: #5a6c7d;
  margin-bottom: 0.5rem;
}

.vulnerability-item .severity {
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.875rem;
}

.vulnerability-item .evidence {
  background: #f8f9fa;
  padding: 0.75rem;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  color: #2c3e50;
  border-left: 3px solid #667eea;
}

.severity-critical {
  border-left: 4px solid #dc3545;
}

.severity-critical .severity {
  background: #dc3545;
  color: white;
}

.severity-high {
  border-left: 4px solid #fd7e14;
}

.severity-high .severity {
  background: #fd7e14;
  color: white;
}

.severity-medium {
  border-left: 4px solid #ffc107;
}

.severity-medium .severity {
  background: #ffc107;
  color: #2c3e50;
}

.severity-low {
  border-left: 4px solid #28a745;
}

.severity-low .severity {
  background: #28a745;
  color: white;
}

/* Footer Styles */
.app-footer {
  background: rgba(0, 0, 0, 0.2);
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
  padding: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Additional Styles for Better Readability */
.password-strength-bar {
  background: #e9ecef;
  border-radius: 4px;
  height: 8px;
  overflow: hidden;
  margin: 0.5rem 0;
}

.password-strength-fill {
  height: 100%;
  transition: width 0.3s ease;
}

.recommendations {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 1rem;
  margin-top: 1rem;
}

.recommendations h4 {
  color: #2c3e50;
  margin-bottom: 0.75rem;
  font-weight: 600;
}

.recommendations ul {
  margin: 0;
  padding-right: 1.5rem;
}

.recommendations li {
  color: #5a6c7d;
  margin-bottom: 0.5rem;
  line-height: 1.5;
}

.scan-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
  margin: 1rem 0;
}

.summary-item {
  text-align: center;
  padding: 1rem;
  background: #ffffff;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.summary-number {
  font-size: 1.5rem;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 0.25rem;
}

.summary-label {
  font-size: 0.875rem;
  color: #5a6c7d;
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
  .app-nav {
    flex-direction: column;
    align-items: center;
  }

  .nav-btn {
    width: 100%;
    max-width: 300px;
  }

  .app-main {
    padding: 1rem;
  }

  .card {
    padding: 1rem;
  }

  .header-content h1 {
    font-size: 2rem;
  }

  .scan-summary {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  }
}
