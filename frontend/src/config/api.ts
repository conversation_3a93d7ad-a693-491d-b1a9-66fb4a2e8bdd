// API Configuration
export const API_BASE_URL = 'http://localhost:8000'

// API Endpoints
export const API_ENDPOINTS = {
  INFO: '/info',
  PASSWORD_SCAN: '/api/v1/password/scan',
  WEB_SCAN: '/api/v1/web/scan',
  SERVER_SCAN: '/api/v1/server/scan',
  PERFORMANCE_STATS: '/api/v1/performance/stats'
}

// Helper function to build full API URLs
export const buildApiUrl = (endpoint: string): string => {
  return `${API_BASE_URL}${endpoint}`
}

// API client with error handling
export const apiClient = {
  async get(endpoint: string) {
    try {
      const response = await fetch(buildApiUrl(endpoint))
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      return await response.json()
    } catch (error) {
      console.error('API GET error:', error)
      throw error
    }
  },

  async post(endpoint: string, data: any) {
    try {
      const response = await fetch(buildApiUrl(endpoint), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
      })
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      return await response.json()
    } catch (error) {
      console.error('API POST error:', error)
      throw error
    }
  }
}
