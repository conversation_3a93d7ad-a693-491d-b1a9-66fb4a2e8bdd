import { useState } from 'react'
import './App.css'
import Dashboard from './components/Dashboard'
import PasswordScanner from './components/PasswordScanner'
import WebScanner from './components/WebScanner'
import ServerScanner from './components/ServerScanner'
import PerformanceStats from './components/PerformanceStats'

function App() {
  const [activeTab, setActiveTab] = useState('dashboard')

  const renderContent = () => {
    switch (activeTab) {
      case 'dashboard':
        return <Dashboard />
      case 'password':
        return <PasswordScanner />
      case 'web':
        return <WebScanner />
      case 'server':
        return <ServerScanner />
      case 'performance':
        return <PerformanceStats />
      default:
        return <Dashboard />
    }
  }

  return (
    <div className="app">
      <header className="app-header">
        <div className="header-content">
          <h1>🔐 SecScan Pro</h1>
          <p>أداة فحص الثغرات الأمنية المتقدمة</p>
        </div>
      </header>

      <nav className="app-nav">
        <button
          className={activeTab === 'dashboard' ? 'nav-btn active' : 'nav-btn'}
          onClick={() => setActiveTab('dashboard')}
        >
          📊 لوحة التحكم
        </button>
        <button
          className={activeTab === 'password' ? 'nav-btn active' : 'nav-btn'}
          onClick={() => setActiveTab('password')}
        >
          🔑 فحص كلمات المرور
        </button>
        <button
          className={activeTab === 'web' ? 'nav-btn active' : 'nav-btn'}
          onClick={() => setActiveTab('web')}
        >
          🌐 فحص ثغرات الويب
        </button>
        <button
          className={activeTab === 'server' ? 'nav-btn active' : 'nav-btn'}
          onClick={() => setActiveTab('server')}
        >
          🖥️ فحص إعدادات الخوادم
        </button>
        <button
          className={activeTab === 'performance' ? 'nav-btn active' : 'nav-btn'}
          onClick={() => setActiveTab('performance')}
        >
          ⚡ إحصائيات الأداء
        </button>
      </nav>

      <main className="app-main">
        {renderContent()}
      </main>

      <footer className="app-footer">
        <p>© 2024 SecScan Pro - تم تطويره لتحسين الأمان السيبراني</p>
      </footer>
    </div>
  )
}

export default App
