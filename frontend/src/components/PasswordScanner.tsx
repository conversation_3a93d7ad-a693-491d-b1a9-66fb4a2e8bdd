import { useState } from 'react'
import { apiClient, API_ENDPOINTS } from '../config/api'

interface PasswordAnalysis {
  password?: string
  strength: string
  score: number
  entropy: number
  length: number
  character_sets: {
    lowercase: boolean
    uppercase: boolean
    digits: boolean
    special: boolean
    arabic: boolean
    spaces: boolean
  }
  patterns: string[]
  vulnerabilities: string[]
  recommendations: string[]
  estimated_crack_time: string
  is_common: boolean
  similarity_to_common: number
}

const PasswordScanner = () => {
  const [password, setPassword] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [analysis, setAnalysis] = useState<PasswordAnalysis | null>(null)
  const [loading, setLoading] = useState(false)

  const analyzePassword = async () => {
    if (!password.trim()) {
      alert('يرجى إدخال كلمة مرور للفحص')
      return
    }

    setLoading(true)
    try {
      const data = await apiClient.post('/api/v1/password/scan', {
        password: password,
        include_password_in_response: false
      })
      setAnalysis(data)
    } catch (error) {
      console.error('Error analyzing password:', error)
      alert('حدث خطأ أثناء تحليل كلمة المرور')
    } finally {
      setLoading(false)
    }
  }

  const getStrengthColor = (strength: string) => {
    switch (strength) {
      case 'ضعيف جداً': return '#dc3545'
      case 'ضعيف': return '#fd7e14'
      case 'متوسط': return '#ffc107'
      case 'قوي': return '#20c997'
      case 'قوي جداً': return '#28a745'
      default: return '#6c757d'
    }
  }

  const getScoreWidth = (score: number) => {
    return `${score}%`
  }

  return (
    <div className="password-scanner">
      <div className="card">
        <h2>🔑 فحص كلمات المرور</h2>
        <p>قم بتحليل قوة كلمة المرور واكتشف نقاط الضعف والتحسينات المطلوبة.</p>

        <div className="form-group">
          <label htmlFor="password">كلمة المرور:</label>
          <div style={{ position: 'relative' }}>
            <input
              type={showPassword ? 'text' : 'password'}
              id="password"
              className="form-input"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="أدخل كلمة المرور للفحص..."
              onKeyPress={(e) => e.key === 'Enter' && analyzePassword()}
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              style={{
                position: 'absolute',
                left: '10px',
                top: '50%',
                transform: 'translateY(-50%)',
                background: 'none',
                border: 'none',
                cursor: 'pointer',
                fontSize: '1.2rem'
              }}
            >
              {showPassword ? '🙈' : '👁️'}
            </button>
          </div>
        </div>

        <button
          className="btn"
          onClick={analyzePassword}
          disabled={loading || !password.trim()}
        >
          {loading ? '🔄 جاري التحليل...' : '🔍 تحليل كلمة المرور'}
        </button>
      </div>

      {analysis && (
        <div className="results">
          <div className="card">
            <h3>📊 نتائج التحليل</h3>

            <div className="strength-indicator">
              <div className="strength-label">
                <strong>مستوى القوة: </strong>
                <span style={{ color: getStrengthColor(analysis.strength) }}>
                  {analysis.strength}
                </span>
              </div>
              <div className="score-bar">
                <div
                  className="score-fill"
                  style={{
                    width: getScoreWidth(analysis.score),
                    backgroundColor: getStrengthColor(analysis.strength)
                  }}
                ></div>
              </div>
              <div className="score-text">{analysis.score}/100 نقطة</div>
            </div>

            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '1rem', marginTop: '1.5rem' }}>
              <div className="info-item">
                <strong>الطول:</strong> {analysis.length} حرف
              </div>
              <div className="info-item">
                <strong>الإنتروبيا:</strong> {analysis.entropy.toFixed(2)} بت
              </div>
              <div className="info-item">
                <strong>وقت الكسر المقدر:</strong> {analysis.estimated_crack_time}
              </div>
              <div className="info-item">
                <strong>كلمة مرور شائعة:</strong> {analysis.is_common ? 'نعم ⚠️' : 'لا ✅'}
              </div>
            </div>
          </div>

          <div className="card">
            <h3>🔤 مجموعات الأحرف المستخدمة</h3>
            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '1rem' }}>
              {Object.entries(analysis.character_sets).map(([key, value]) => (
                <div key={key} className={`charset-item ${value ? 'enabled' : 'disabled'}`}>
                  <span>{value ? '✅' : '❌'}</span>
                  <span>
                    {key === 'lowercase' && 'أحرف صغيرة (a-z)'}
                    {key === 'uppercase' && 'أحرف كبيرة (A-Z)'}
                    {key === 'digits' && 'أرقام (0-9)'}
                    {key === 'special' && 'رموز خاصة (!@#$)'}
                    {key === 'arabic' && 'أحرف عربية'}
                    {key === 'spaces' && 'مسافات'}
                  </span>
                </div>
              ))}
            </div>
          </div>

          {analysis.vulnerabilities.length > 0 && (
            <div className="card">
              <h3>⚠️ نقاط الضعف</h3>
              <ul className="vulnerability-list">
                {analysis.vulnerabilities.map((vuln, index) => (
                  <li key={index} className="vulnerability-item">
                    {vuln}
                  </li>
                ))}
              </ul>
            </div>
          )}

          {analysis.patterns.length > 0 && (
            <div className="card">
              <h3>🔍 الأنماط المكتشفة</h3>
              <ul className="pattern-list">
                {analysis.patterns.map((pattern, index) => (
                  <li key={index} className="pattern-item">
                    {pattern}
                  </li>
                ))}
              </ul>
            </div>
          )}

          <div className="card">
            <h3>💡 توصيات التحسين</h3>
            <ul className="recommendation-list">
              {analysis.recommendations.map((rec, index) => (
                <li key={index} className="recommendation-item">
                  {rec}
                </li>
              ))}
            </ul>
          </div>
        </div>
      )}

      <style jsx>{`
        .strength-indicator {
          margin: 1.5rem 0;
        }

        .strength-label {
          margin-bottom: 0.5rem;
          font-size: 1.1rem;
          color: #2c3e50;
        }

        .strength-label strong {
          color: #2c3e50;
          font-weight: 600;
        }

        .score-bar {
          width: 100%;
          height: 20px;
          background: #e9ecef;
          border-radius: 10px;
          overflow: hidden;
          margin-bottom: 0.5rem;
        }

        .score-fill {
          height: 100%;
          transition: width 0.5s ease;
        }

        .score-text {
          text-align: center;
          font-weight: bold;
          color: #2c3e50;
        }

        .info-item {
          padding: 1rem;
          background: #ffffff;
          border-radius: 8px;
          border: 1px solid #dee2e6;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .info-item strong {
          color: #2c3e50;
          font-weight: 600;
        }

        .charset-item {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          padding: 0.75rem;
          border-radius: 8px;
          background: #ffffff;
          border: 1px solid #dee2e6;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
          color: #2c3e50;
          font-weight: 500;
        }

        .charset-item.enabled {
          border-left: 4px solid #28a745;
        }

        .charset-item.disabled {
          border-left: 4px solid #dc3545;
          opacity: 0.7;
        }

        .vulnerability-list,
        .pattern-list,
        .recommendation-list {
          list-style: none;
          padding: 0;
        }

        .vulnerability-item,
        .pattern-item,
        .recommendation-item {
          padding: 0.75rem;
          margin-bottom: 0.5rem;
          border-radius: 8px;
          background: #ffffff;
          border: 1px solid #dee2e6;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
          color: #2c3e50;
          line-height: 1.5;
        }

        .vulnerability-item {
          border-left: 4px solid #dc3545;
        }

        .pattern-item {
          border-left: 4px solid #ffc107;
        }

        .recommendation-item {
          border-left: 4px solid #17a2b8;
        }
      `}</style>
    </div>
  )
}

export default PasswordScanner
