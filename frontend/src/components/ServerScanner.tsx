import { useState } from 'react'

interface ServerIssue {
  type: string
  severity: string
  title: string
  description: string
  evidence: string
  impact: string
  remediation: string
  port?: number
  service?: string
}

interface SSLInfo {
  version: string
  cipher: string
  certificate_valid: boolean
  certificate_expired: boolean
  certificate_self_signed: boolean
  certificate_issuer: string
  certificate_subject: string
  certificate_expiry?: string
  supported_protocols: string[]
  weak_ciphers: string[]
}

interface SecurityHeaders {
  strict_transport_security?: string
  content_security_policy?: string
  x_frame_options?: string
  x_content_type_options?: string
  x_xss_protection?: string
  referrer_policy?: string
  permissions_policy?: string
}

interface ServerScanResult {
  target: string
  ip_address: string
  open_ports: number[]
  services: { [key: number]: string }
  ssl_info?: SSLInfo
  security_headers: SecurityHeaders
  server_info: { [key: string]: string }
  issues: ServerIssue[]
  scan_duration: number
  summary: {
    total: number
    critical: number
    high: number
    medium: number
    low: number
    info: number
  }
  recommendations: string[]
}

const ServerScanner = () => {
  const [target, setTarget] = useState('')
  const [portScan, setPortScan] = useState(true)
  const [sslScan, setSslScan] = useState(true)
  const [headerScan, setHeaderScan] = useState(true)
  const [scanResult, setScanResult] = useState<ServerScanResult | null>(null)
  const [loading, setLoading] = useState(false)

  const startScan = async () => {
    if (!target.trim()) {
      alert('يرجى إدخال عنوان الخادم للفحص')
      return
    }

    setLoading(true)
    setScanResult(null)

    try {
      const response = await fetch('/api/v1/server/scan', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          target: target,
          port_scan: portScan,
          ssl_scan: sslScan,
          header_scan: headerScan
        }),
      })

      if (!response.ok) {
        throw new Error('فشل في فحص الخادم')
      }

      const data = await response.json()
      setScanResult(data)
    } catch (error) {
      console.error('Error scanning server:', error)
      alert('حدث خطأ أثناء فحص الخادم')
    } finally {
      setLoading(false)
    }
  }

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'حرج': return '#dc3545'
      case 'عالي': return '#fd7e14'
      case 'متوسط': return '#ffc107'
      case 'منخفض': return '#28a745'
      case 'معلوماتي': return '#17a2b8'
      default: return '#6c757d'
    }
  }

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'حرج': return '🚨'
      case 'عالي': return '⚠️'
      case 'متوسط': return '⚡'
      case 'منخفض': return 'ℹ️'
      case 'معلوماتي': return '📋'
      default: return '❓'
    }
  }

  return (
    <div className="server-scanner">
      <div className="card">
        <h2>🖥️ فحص إعدادات الخوادم</h2>
        <p>قم بفحص إعدادات الأمان للخوادم بما في ذلك SSL/TLS ورؤوس الأمان والبورتات المفتوحة.</p>

        <div className="form-group">
          <label htmlFor="target">عنوان الخادم:</label>
          <input
            type="text"
            id="target"
            className="form-input"
            value={target}
            onChange={(e) => setTarget(e.target.value)}
            placeholder="example.com أو 192.168.1.1"
            onKeyPress={(e) => e.key === 'Enter' && startScan()}
          />
        </div>

        <div className="scan-options">
          <h3>خيارات الفحص:</h3>
          <div className="checkbox-group">
            <label className="checkbox-label">
              <input
                type="checkbox"
                checked={portScan}
                onChange={(e) => setPortScan(e.target.checked)}
              />
              <span>فحص البورتات المفتوحة</span>
            </label>
            <label className="checkbox-label">
              <input
                type="checkbox"
                checked={sslScan}
                onChange={(e) => setSslScan(e.target.checked)}
              />
              <span>فحص إعدادات SSL/TLS</span>
            </label>
            <label className="checkbox-label">
              <input
                type="checkbox"
                checked={headerScan}
                onChange={(e) => setHeaderScan(e.target.checked)}
              />
              <span>فحص رؤوس الأمان HTTP</span>
            </label>
          </div>
        </div>

        <button
          className="btn"
          onClick={startScan}
          disabled={loading || !target.trim()}
        >
          {loading ? '🔄 جاري الفحص...' : '🔍 بدء الفحص'}
        </button>
      </div>

      {loading && (
        <div className="card">
          <div className="loading">
            <div className="spinner"></div>
            <p>جاري فحص الخادم... قد يستغرق هذا عدة دقائق</p>
          </div>
        </div>
      )}

      {scanResult && (
        <div className="results">
          <div className="card">
            <h3>📊 معلومات الخادم</h3>
            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '1rem' }}>
              <div className="info-item">
                <strong>الهدف:</strong> {scanResult.target}
              </div>
              <div className="info-item">
                <strong>عنوان IP:</strong> {scanResult.ip_address}
              </div>
              <div className="info-item">
                <strong>البورتات المفتوحة:</strong> {scanResult.open_ports.length}
              </div>
              <div className="info-item">
                <strong>مدة الفحص:</strong> {scanResult.scan_duration.toFixed(2)} ثانية
              </div>
            </div>
          </div>

          <div className="card">
            <h3>📈 ملخص المشاكل</h3>
            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))', gap: '1rem' }}>
              <div className="stat-item">
                <div className="stat-number">{scanResult.summary.total}</div>
                <div className="stat-label">إجمالي المشاكل</div>
              </div>
              <div className="stat-item critical">
                <div className="stat-number">{scanResult.summary.critical}</div>
                <div className="stat-label">حرجة</div>
              </div>
              <div className="stat-item high">
                <div className="stat-number">{scanResult.summary.high}</div>
                <div className="stat-label">عالية</div>
              </div>
              <div className="stat-item medium">
                <div className="stat-number">{scanResult.summary.medium}</div>
                <div className="stat-label">متوسطة</div>
              </div>
              <div className="stat-item low">
                <div className="stat-number">{scanResult.summary.low}</div>
                <div className="stat-label">منخفضة</div>
              </div>
            </div>
          </div>

          {scanResult.open_ports.length > 0 && (
            <div className="card">
              <h3>🔌 البورتات والخدمات</h3>
              <div className="ports-grid">
                {scanResult.open_ports.map(port => (
                  <div key={port} className="port-item">
                    <span className="port-number">{port}</span>
                    <span className="service-name">{scanResult.services[port] || 'غير معروف'}</span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {scanResult.ssl_info && (
            <div className="card">
              <h3>🔒 معلومات SSL/TLS</h3>
              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '1rem' }}>
                <div className="info-item">
                  <strong>الإصدار:</strong> {scanResult.ssl_info.version}
                </div>
                <div className="info-item">
                  <strong>التشفير:</strong> {scanResult.ssl_info.cipher}
                </div>
                <div className="info-item">
                  <strong>صحة الشهادة:</strong> {scanResult.ssl_info.certificate_valid ? '✅ صحيحة' : '❌ غير صحيحة'}
                </div>
                <div className="info-item">
                  <strong>انتهاء الصلاحية:</strong> {scanResult.ssl_info.certificate_expired ? '❌ منتهية' : '✅ سارية'}
                </div>
              </div>

              {scanResult.ssl_info.supported_protocols.length > 0 && (
                <div style={{ marginTop: '1rem' }}>
                  <strong>البروتوكولات المدعومة:</strong>
                  <div className="protocols-list">
                    {scanResult.ssl_info.supported_protocols.map(protocol => (
                      <span key={protocol} className="protocol-badge">{protocol}</span>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {scanResult.issues.length > 0 ? (
            <div className="card">
              <h3>🚨 المشاكل المكتشفة</h3>
              <div className="issues-list">
                {scanResult.issues.map((issue, index) => (
                  <div key={index} className="issue-card">
                    <div className="issue-header">
                      <span className="severity-badge" style={{ backgroundColor: getSeverityColor(issue.severity) }}>
                        {getSeverityIcon(issue.severity)} {issue.severity}
                      </span>
                      <h4>{issue.title}</h4>
                    </div>

                    <div className="issue-details">
                      <p><strong>الوصف:</strong> {issue.description}</p>
                      <p><strong>التأثير:</strong> {issue.impact}</p>
                      <p><strong>طريقة الإصلاح:</strong> {issue.remediation}</p>
                      {issue.port && <p><strong>البورت:</strong> {issue.port}</p>}
                      {issue.service && <p><strong>الخدمة:</strong> {issue.service}</p>}

                      <details>
                        <summary>الدليل</summary>
                        <div className="evidence">
                          <code>{issue.evidence}</code>
                        </div>
                      </details>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <div className="card">
              <h3>✅ لم يتم اكتشاف مشاكل</h3>
              <p>لم يتم العثور على مشاكل أمنية واضحة في إعدادات الخادم.</p>
            </div>
          )}

          <div className="card">
            <h3>💡 التوصيات</h3>
            <ul className="recommendations-list">
              {scanResult.recommendations.map((rec, index) => (
                <li key={index}>{rec}</li>
              ))}
            </ul>
          </div>
        </div>
      )}

      <style jsx>{`
        .scan-options {
          margin: 1.5rem 0;
        }

        .checkbox-group {
          display: flex;
          flex-direction: column;
          gap: 0.75rem;
          margin-top: 0.5rem;
        }

        .checkbox-label {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          cursor: pointer;
        }

        .checkbox-label input[type="checkbox"] {
          width: 18px;
          height: 18px;
        }

        .stat-item {
          text-align: center;
          padding: 1rem;
          background: #f8f9fa;
          border-radius: 8px;
          border: 1px solid #dee2e6;
        }

        .stat-item.critical { border-left: 4px solid #dc3545; }
        .stat-item.high { border-left: 4px solid #fd7e14; }
        .stat-item.medium { border-left: 4px solid #ffc107; }
        .stat-item.low { border-left: 4px solid #28a745; }

        .stat-number {
          font-size: 2rem;
          font-weight: bold;
          margin-bottom: 0.25rem;
        }

        .stat-label {
          font-size: 0.9rem;
          color: #666;
        }

        .info-item {
          padding: 1rem;
          background: #f8f9fa;
          border-radius: 8px;
          border: 1px solid #dee2e6;
        }

        .ports-grid {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
          gap: 0.5rem;
        }

        .port-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          padding: 1rem;
          background: #f8f9fa;
          border: 1px solid #dee2e6;
          border-radius: 8px;
        }

        .port-number {
          font-size: 1.5rem;
          font-weight: bold;
          color: #495057;
        }

        .service-name {
          font-size: 0.9rem;
          color: #6c757d;
        }

        .protocols-list {
          display: flex;
          flex-wrap: wrap;
          gap: 0.5rem;
          margin-top: 0.5rem;
        }

        .protocol-badge {
          background: #e9ecef;
          padding: 0.25rem 0.5rem;
          border-radius: 4px;
          font-size: 0.8rem;
        }

        .issues-list {
          display: flex;
          flex-direction: column;
          gap: 1rem;
        }

        .issue-card {
          border: 1px solid #dee2e6;
          border-radius: 8px;
          overflow: hidden;
        }

        .issue-header {
          background: #f8f9fa;
          padding: 1rem;
          display: flex;
          align-items: center;
          gap: 1rem;
        }

        .severity-badge {
          color: white;
          padding: 0.25rem 0.75rem;
          border-radius: 20px;
          font-size: 0.8rem;
          font-weight: bold;
        }

        .issue-header h4 {
          margin: 0;
          color: #333;
        }

        .issue-details {
          padding: 1rem;
        }

        .issue-details p {
          margin-bottom: 0.5rem;
        }

        .evidence {
          background: #f8f9fa;
          padding: 1rem;
          border-radius: 4px;
          margin-top: 0.5rem;
        }

        .evidence code {
          display: block;
          background: #e9ecef;
          padding: 0.5rem;
          border-radius: 4px;
          font-family: 'Courier New', monospace;
          word-break: break-all;
        }

        .recommendations-list {
          list-style: none;
          padding: 0;
        }

        .recommendations-list li {
          padding: 0.75rem;
          margin-bottom: 0.5rem;
          background: #f8f9fa;
          border: 1px solid #dee2e6;
          border-left: 4px solid #17a2b8;
          border-radius: 4px;
        }
      `}</style>
    </div>
  )
}

export default ServerScanner
