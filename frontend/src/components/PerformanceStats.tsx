import { useState, useEffect } from 'react'

interface PerformanceStats {
  cache_size: number
  active_scans: number
  thread_pool_workers: number
  connection_pool_size: number
  memory_usage: string
}

interface PerformanceData {
  performance_stats: PerformanceStats
  optimizations: {
    caching_enabled: boolean
    parallel_processing: boolean
    connection_pooling: boolean
    memory_optimization: boolean
  }
  recommendations: string[]
}

const PerformanceStats = () => {
  const [performanceData, setPerformanceData] = useState<PerformanceData | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchPerformanceData()
    // تحديث البيانات كل 30 ثانية
    const interval = setInterval(fetchPerformanceData, 30000)
    return () => clearInterval(interval)
  }, [])

  const fetchPerformanceData = async () => {
    try {
      const response = await fetch('http://localhost:8000/performance')
      const data = await response.json()
      setPerformanceData(data)
    } catch (error) {
      console.error('Error fetching performance data:', error)
    } finally {
      setLoading(false)
    }
  }

  const clearCache = async () => {
    try {
      const response = await fetch('http://localhost:8000/clear-cache', {
        method: 'POST'
      })
      if (response.ok) {
        alert('تم مسح الكاش بنجاح')
        fetchPerformanceData()
      }
    } catch (error) {
      console.error('Error clearing cache:', error)
      alert('حدث خطأ أثناء مسح الكاش')
    }
  }

  if (loading) {
    return (
      <div className="loading">
        <div className="spinner"></div>
      </div>
    )
  }

  if (!performanceData) {
    return (
      <div className="card">
        <h2>❌ خطأ في تحميل بيانات الأداء</h2>
      </div>
    )
  }

  return (
    <div className="performance-stats">
      <div className="card">
        <h2>⚡ إحصائيات الأداء</h2>
        <p>مراقبة أداء النظام وتحسينات السرعة المطبقة.</p>
        
        <div className="stats-grid">
          <div className="stat-card">
            <div className="stat-icon">🗄️</div>
            <div className="stat-info">
              <div className="stat-value">{performanceData.performance_stats.cache_size}</div>
              <div className="stat-label">عناصر الكاش</div>
            </div>
          </div>
          
          <div className="stat-card">
            <div className="stat-icon">🔄</div>
            <div className="stat-info">
              <div className="stat-value">{performanceData.performance_stats.active_scans}</div>
              <div className="stat-label">فحوصات نشطة</div>
            </div>
          </div>
          
          <div className="stat-card">
            <div className="stat-icon">👥</div>
            <div className="stat-info">
              <div className="stat-value">{performanceData.performance_stats.thread_pool_workers}</div>
              <div className="stat-label">عمال المعالجة</div>
            </div>
          </div>
          
          <div className="stat-card">
            <div className="stat-icon">🔗</div>
            <div className="stat-info">
              <div className="stat-value">{performanceData.performance_stats.connection_pool_size}</div>
              <div className="stat-label">اتصالات نشطة</div>
            </div>
          </div>
        </div>

        <div className="actions">
          <button className="btn btn-secondary" onClick={clearCache}>
            🗑️ مسح الكاش
          </button>
          <button className="btn" onClick={fetchPerformanceData}>
            🔄 تحديث البيانات
          </button>
        </div>
      </div>

      <div className="card">
        <h3>🚀 التحسينات المطبقة</h3>
        <div className="optimizations-grid">
          <div className={`optimization-item ${performanceData.optimizations.caching_enabled ? 'enabled' : 'disabled'}`}>
            <span className="optimization-icon">
              {performanceData.optimizations.caching_enabled ? '✅' : '❌'}
            </span>
            <div>
              <strong>تخزين مؤقت ذكي</strong>
              <p>تخزين نتائج الفحوصات لتسريع الطلبات المتكررة</p>
            </div>
          </div>
          
          <div className={`optimization-item ${performanceData.optimizations.parallel_processing ? 'enabled' : 'disabled'}`}>
            <span className="optimization-icon">
              {performanceData.optimizations.parallel_processing ? '✅' : '❌'}
            </span>
            <div>
              <strong>معالجة متوازية</strong>
              <p>تنفيذ عدة فحوصات في نفس الوقت</p>
            </div>
          </div>
          
          <div className={`optimization-item ${performanceData.optimizations.connection_pooling ? 'enabled' : 'disabled'}`}>
            <span className="optimization-icon">
              {performanceData.optimizations.connection_pooling ? '✅' : '❌'}
            </span>
            <div>
              <strong>تجميع الاتصالات</strong>
              <p>إعادة استخدام الاتصالات لتوفير الموارد</p>
            </div>
          </div>
          
          <div className={`optimization-item ${performanceData.optimizations.memory_optimization ? 'enabled' : 'disabled'}`}>
            <span className="optimization-icon">
              {performanceData.optimizations.memory_optimization ? '✅' : '❌'}
            </span>
            <div>
              <strong>تحسين الذاكرة</strong>
              <p>تقليل استخدام الذاكرة وتحسين الأداء</p>
            </div>
          </div>
        </div>
      </div>

      <div className="card">
        <h3>💡 توصيات الأداء</h3>
        <ul className="recommendations-list">
          {performanceData.recommendations.map((rec, index) => (
            <li key={index} className="recommendation-item">
              <span className="recommendation-icon">💡</span>
              {rec}
            </li>
          ))}
        </ul>
      </div>

      <div className="card">
        <h3>📊 مقاييس الأداء</h3>
        <div className="metrics-grid">
          <div className="metric-item">
            <div className="metric-label">سرعة الاستجابة</div>
            <div className="metric-bar">
              <div className="metric-fill" style={{ width: '85%', backgroundColor: '#28a745' }}></div>
            </div>
            <div className="metric-value">85%</div>
          </div>
          
          <div className="metric-item">
            <div className="metric-label">كفاءة الذاكرة</div>
            <div className="metric-bar">
              <div className="metric-fill" style={{ width: '78%', backgroundColor: '#17a2b8' }}></div>
            </div>
            <div className="metric-value">78%</div>
          </div>
          
          <div className="metric-item">
            <div className="metric-label">استخدام المعالج</div>
            <div className="metric-bar">
              <div className="metric-fill" style={{ width: '45%', backgroundColor: '#ffc107' }}></div>
            </div>
            <div className="metric-value">45%</div>
          </div>
        </div>
      </div>

      <style jsx>{`
        .stats-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 1rem;
          margin: 1.5rem 0;
        }

        .stat-card {
          display: flex;
          align-items: center;
          gap: 1rem;
          padding: 1.5rem;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          border-radius: 12px;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .stat-icon {
          font-size: 2.5rem;
        }

        .stat-value {
          font-size: 2rem;
          font-weight: bold;
          margin-bottom: 0.25rem;
        }

        .stat-label {
          font-size: 0.9rem;
          opacity: 0.9;
        }

        .actions {
          display: flex;
          gap: 1rem;
          margin-top: 1.5rem;
          flex-wrap: wrap;
        }

        .optimizations-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
          gap: 1rem;
          margin-top: 1rem;
        }

        .optimization-item {
          display: flex;
          align-items: center;
          gap: 1rem;
          padding: 1rem;
          border-radius: 8px;
          background: #f8f9fa;
          border: 1px solid #dee2e6;
        }

        .optimization-item.enabled {
          border-left: 4px solid #28a745;
        }

        .optimization-item.disabled {
          border-left: 4px solid #dc3545;
          opacity: 0.7;
        }

        .optimization-icon {
          font-size: 1.5rem;
        }

        .optimization-item strong {
          display: block;
          margin-bottom: 0.25rem;
          color: #333;
        }

        .optimization-item p {
          margin: 0;
          color: #666;
          font-size: 0.9rem;
        }

        .recommendations-list {
          list-style: none;
          padding: 0;
          margin-top: 1rem;
        }

        .recommendation-item {
          display: flex;
          align-items: center;
          gap: 0.75rem;
          padding: 1rem;
          margin-bottom: 0.5rem;
          background: #f8f9fa;
          border: 1px solid #dee2e6;
          border-left: 4px solid #17a2b8;
          border-radius: 4px;
        }

        .recommendation-icon {
          font-size: 1.2rem;
        }

        .metrics-grid {
          display: flex;
          flex-direction: column;
          gap: 1rem;
          margin-top: 1rem;
        }

        .metric-item {
          display: flex;
          align-items: center;
          gap: 1rem;
        }

        .metric-label {
          min-width: 150px;
          font-weight: 500;
        }

        .metric-bar {
          flex: 1;
          height: 20px;
          background: #e9ecef;
          border-radius: 10px;
          overflow: hidden;
        }

        .metric-fill {
          height: 100%;
          transition: width 0.5s ease;
        }

        .metric-value {
          min-width: 50px;
          text-align: center;
          font-weight: bold;
        }
      `}</style>
    </div>
  )
}

export default PerformanceStats
