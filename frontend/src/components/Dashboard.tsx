import { useState, useEffect } from 'react'
import { apiClient, API_ENDPOINTS } from '../config/api'

interface SystemInfo {
  system: string
  version: string
  environment: string
  features: {
    password_scanner: boolean
    web_vulnerability_scanner: boolean
    server_config_scanner: boolean
    report_generator: boolean
  }
  statistics: {
    total_scans: number
    vulnerabilities_found: number
    reports_generated: number
  }
}

const Dashboard = () => {
  const [systemInfo, setSystemInfo] = useState<SystemInfo | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchSystemInfo()
  }, [])

  const fetchSystemInfo = async () => {
    try {
      const data = await apiClient.get(API_ENDPOINTS.INFO)
      setSystemInfo(data)
    } catch (error) {
      console.error('Error fetching system info:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="loading">
        <div className="spinner"></div>
      </div>
    )
  }

  return (
    <div className="dashboard">
      <div className="card">
        <h2>🏠 مرحباً بك في SecScan Pro</h2>
        <p>أداة فحص الثغرات الأمنية المتقدمة التي تساعدك في حماية تطبيقاتك وخوادمك من التهديدات السيبرانية.</p>
      </div>

      {systemInfo && (
        <>
          <div className="card">
            <h3>📊 معلومات النظام</h3>
            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '1rem' }}>
              <div>
                <strong>النظام:</strong> {systemInfo.system}
              </div>
              <div>
                <strong>الإصدار:</strong> {systemInfo.version}
              </div>
              <div>
                <strong>البيئة:</strong> {systemInfo.environment}
              </div>
            </div>
          </div>

          <div className="card">
            <h3>🔧 الميزات المتاحة</h3>
            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '1rem' }}>
              <div className={`feature-item ${systemInfo.features.password_scanner ? 'enabled' : 'disabled'}`}>
                <span>🔑</span>
                <div>
                  <strong>فحص كلمات المرور</strong>
                  <p>تحليل قوة كلمات المرور واكتشاف الأنماط الضعيفة</p>
                </div>
              </div>
              <div className={`feature-item ${systemInfo.features.web_vulnerability_scanner ? 'enabled' : 'disabled'}`}>
                <span>🌐</span>
                <div>
                  <strong>فحص ثغرات الويب</strong>
                  <p>اكتشاف SQL Injection, XSS, CSRF وثغرات أخرى</p>
                </div>
              </div>
              <div className={`feature-item ${systemInfo.features.server_config_scanner ? 'enabled' : 'disabled'}`}>
                <span>🖥️</span>
                <div>
                  <strong>فحص إعدادات الخوادم</strong>
                  <p>تحليل إعدادات SSL/TLS والأمان العامة</p>
                </div>
              </div>
              <div className={`feature-item ${systemInfo.features.report_generator ? 'enabled' : 'disabled'}`}>
                <span>📊</span>
                <div>
                  <strong>مولد التقارير</strong>
                  <p>تقارير مفصلة مع توصيات الإصلاح</p>
                </div>
              </div>
            </div>
          </div>

          <div className="card">
            <h3>📈 الإحصائيات</h3>
            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '1rem' }}>
              <div className="stat-item">
                <div className="stat-number">{systemInfo.statistics.total_scans}</div>
                <div className="stat-label">إجمالي الفحوصات</div>
              </div>
              <div className="stat-item">
                <div className="stat-number">{systemInfo.statistics.vulnerabilities_found}</div>
                <div className="stat-label">الثغرات المكتشفة</div>
              </div>
              <div className="stat-item">
                <div className="stat-number">{systemInfo.statistics.reports_generated}</div>
                <div className="stat-label">التقارير المولدة</div>
              </div>
            </div>
          </div>
        </>
      )}

      <div className="card">
        <h3>🚀 ابدأ الفحص</h3>
        <p>اختر نوع الفحص الذي تريد تشغيله من القائمة العلوية:</p>
        <div style={{ display: 'flex', gap: '1rem', flexWrap: 'wrap', marginTop: '1rem' }}>
          <div className="quick-action">
            <span>🔑</span>
            <strong>فحص كلمة مرور</strong>
            <p>اختبر قوة كلمة مرور معينة</p>
          </div>
          <div className="quick-action">
            <span>🌐</span>
            <strong>فحص موقع ويب</strong>
            <p>ابحث عن ثغرات في تطبيق ويب</p>
          </div>
          <div className="quick-action">
            <span>🖥️</span>
            <strong>فحص خادم</strong>
            <p>تحليل إعدادات الأمان للخادم</p>
          </div>
        </div>
      </div>

      <style jsx>{`
        .feature-item {
          display: flex;
          align-items: center;
          gap: 1rem;
          padding: 1rem;
          border-radius: 8px;
          background: #f8f9fa;
          border: 1px solid #dee2e6;
        }

        .feature-item.enabled {
          border-left: 4px solid #28a745;
        }

        .feature-item.disabled {
          border-left: 4px solid #dc3545;
          opacity: 0.6;
        }

        .feature-item span {
          font-size: 2rem;
        }

        .feature-item strong {
          display: block;
          margin-bottom: 0.25rem;
          color: #333;
        }

        .feature-item p {
          margin: 0;
          color: #666;
          font-size: 0.9rem;
        }

        .stat-item {
          text-align: center;
          padding: 1.5rem;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          border-radius: 12px;
        }

        .stat-number {
          font-size: 2.5rem;
          font-weight: bold;
          margin-bottom: 0.5rem;
        }

        .stat-label {
          font-size: 1rem;
          opacity: 0.9;
        }

        .quick-action {
          flex: 1;
          min-width: 200px;
          padding: 1.5rem;
          background: #f8f9fa;
          border: 1px solid #dee2e6;
          border-radius: 12px;
          text-align: center;
          cursor: pointer;
          transition: all 0.3s ease;
        }

        .quick-action:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .quick-action span {
          display: block;
          font-size: 2.5rem;
          margin-bottom: 0.5rem;
        }

        .quick-action strong {
          display: block;
          margin-bottom: 0.5rem;
          color: #333;
        }

        .quick-action p {
          margin: 0;
          color: #666;
          font-size: 0.9rem;
        }
      `}</style>
    </div>
  )
}

export default Dashboard
