import { useState } from 'react'

interface Vulnerability {
  type: string
  severity: string
  url: string
  parameter?: string
  payload: string
  evidence: string
  description: string
  impact: string
  remediation: string
  confidence: number
}

interface WebScanResult {
  target_url: string
  scan_status: string
  vulnerabilities: Vulnerability[]
  scan_duration: number
  pages_scanned: number
  forms_found: number
  parameters_tested: number
  summary: {
    total: number
    critical: number
    high: number
    medium: number
    low: number
  }
  recommendations: string[]
}

const WebScanner = () => {
  const [targetUrl, setTargetUrl] = useState('')
  const [maxDepth, setMaxDepth] = useState(2)
  const [scanResult, setScanResult] = useState<WebScanResult | null>(null)
  const [loading, setLoading] = useState(false)

  const startScan = async () => {
    if (!targetUrl.trim()) {
      alert('يرجى إدخال رابط الموقع للفحص')
      return
    }

    setLoading(true)
    setScanResult(null)
    
    try {
      const response = await fetch('http://localhost:8000/api/v1/web/scan', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          target_url: targetUrl,
          max_depth: maxDepth,
          scan_types: ["sql_injection", "xss", "directory_traversal", "command_injection", "csrf"],
          aggressive_mode: false
        }),
      })

      if (!response.ok) {
        throw new Error('فشل في فحص الموقع')
      }

      const data = await response.json()
      setScanResult(data)
    } catch (error) {
      console.error('Error scanning website:', error)
      alert('حدث خطأ أثناء فحص الموقع')
    } finally {
      setLoading(false)
    }
  }

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'حرج': return '#dc3545'
      case 'عالي': return '#fd7e14'
      case 'متوسط': return '#ffc107'
      case 'منخفض': return '#28a745'
      default: return '#6c757d'
    }
  }

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'حرج': return '🚨'
      case 'عالي': return '⚠️'
      case 'متوسط': return '⚡'
      case 'منخفض': return 'ℹ️'
      default: return '❓'
    }
  }

  return (
    <div className="web-scanner">
      <div className="card">
        <h2>🌐 فحص ثغرات تطبيقات الويب</h2>
        <p>قم بفحص تطبيقات الويب للبحث عن الثغرات الأمنية الشائعة مثل SQL Injection و XSS و CSRF.</p>
        
        <div className="form-group">
          <label htmlFor="target-url">رابط الموقع:</label>
          <input
            type="url"
            id="target-url"
            className="form-input"
            value={targetUrl}
            onChange={(e) => setTargetUrl(e.target.value)}
            placeholder="https://example.com"
            onKeyPress={(e) => e.key === 'Enter' && startScan()}
          />
        </div>

        <div className="form-group">
          <label htmlFor="max-depth">عمق الاستكشاف:</label>
          <select
            id="max-depth"
            className="form-input"
            value={maxDepth}
            onChange={(e) => setMaxDepth(parseInt(e.target.value))}
          >
            <option value={1}>سطحي (1 مستوى)</option>
            <option value={2}>متوسط (2 مستوى)</option>
            <option value={3}>عميق (3 مستوى)</option>
          </select>
        </div>

        <button 
          className="btn" 
          onClick={startScan}
          disabled={loading || !targetUrl.trim()}
        >
          {loading ? '🔄 جاري الفحص...' : '🔍 بدء الفحص'}
        </button>
      </div>

      {loading && (
        <div className="card">
          <div className="loading">
            <div className="spinner"></div>
            <p>جاري فحص الموقع... قد يستغرق هذا عدة دقائق</p>
          </div>
        </div>
      )}

      {scanResult && (
        <div className="results">
          <div className="card">
            <h3>📊 ملخص النتائج</h3>
            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '1rem' }}>
              <div className="stat-item">
                <div className="stat-number">{scanResult.summary.total}</div>
                <div className="stat-label">إجمالي الثغرات</div>
              </div>
              <div className="stat-item critical">
                <div className="stat-number">{scanResult.summary.critical}</div>
                <div className="stat-label">حرجة</div>
              </div>
              <div className="stat-item high">
                <div className="stat-number">{scanResult.summary.high}</div>
                <div className="stat-label">عالية</div>
              </div>
              <div className="stat-item medium">
                <div className="stat-number">{scanResult.summary.medium}</div>
                <div className="stat-label">متوسطة</div>
              </div>
              <div className="stat-item low">
                <div className="stat-number">{scanResult.summary.low}</div>
                <div className="stat-label">منخفضة</div>
              </div>
            </div>

            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '1rem', marginTop: '1rem' }}>
              <div className="info-item">
                <strong>الصفحات المفحوصة:</strong> {scanResult.pages_scanned}
              </div>
              <div className="info-item">
                <strong>النماذج الموجودة:</strong> {scanResult.forms_found}
              </div>
              <div className="info-item">
                <strong>المعاملات المختبرة:</strong> {scanResult.parameters_tested}
              </div>
              <div className="info-item">
                <strong>مدة الفحص:</strong> {scanResult.scan_duration.toFixed(2)} ثانية
              </div>
            </div>
          </div>

          {scanResult.vulnerabilities.length > 0 ? (
            <div className="card">
              <h3>🚨 الثغرات المكتشفة</h3>
              <div className="vulnerabilities-list">
                {scanResult.vulnerabilities.map((vuln, index) => (
                  <div key={index} className="vulnerability-card">
                    <div className="vulnerability-header">
                      <span className="severity-badge" style={{ backgroundColor: getSeverityColor(vuln.severity) }}>
                        {getSeverityIcon(vuln.severity)} {vuln.severity}
                      </span>
                      <h4>{vuln.type}</h4>
                    </div>
                    
                    <div className="vulnerability-details">
                      <p><strong>الوصف:</strong> {vuln.description}</p>
                      <p><strong>الرابط:</strong> {vuln.url}</p>
                      {vuln.parameter && <p><strong>المعامل:</strong> {vuln.parameter}</p>}
                      <p><strong>التأثير:</strong> {vuln.impact}</p>
                      <p><strong>طريقة الإصلاح:</strong> {vuln.remediation}</p>
                      <p><strong>مستوى الثقة:</strong> {(vuln.confidence * 100).toFixed(0)}%</p>
                      
                      <details>
                        <summary>تفاصيل تقنية</summary>
                        <div className="technical-details">
                          <p><strong>الحمولة المستخدمة:</strong></p>
                          <code>{vuln.payload}</code>
                          <p><strong>الدليل:</strong></p>
                          <code>{vuln.evidence}</code>
                        </div>
                      </details>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <div className="card">
              <h3>✅ لم يتم اكتشاف ثغرات</h3>
              <p>لم يتم العثور على ثغرات واضحة في الفحص الحالي. هذا لا يعني أن الموقع آمن تماماً، يُنصح بإجراء فحوصات إضافية.</p>
            </div>
          )}

          <div className="card">
            <h3>💡 التوصيات</h3>
            <ul className="recommendations-list">
              {scanResult.recommendations.map((rec, index) => (
                <li key={index}>{rec}</li>
              ))}
            </ul>
          </div>
        </div>
      )}

      <style jsx>{`
        .stat-item {
          text-align: center;
          padding: 1rem;
          background: #f8f9fa;
          border-radius: 8px;
          border: 1px solid #dee2e6;
        }

        .stat-item.critical { border-left: 4px solid #dc3545; }
        .stat-item.high { border-left: 4px solid #fd7e14; }
        .stat-item.medium { border-left: 4px solid #ffc107; }
        .stat-item.low { border-left: 4px solid #28a745; }

        .stat-number {
          font-size: 2rem;
          font-weight: bold;
          margin-bottom: 0.25rem;
        }

        .stat-label {
          font-size: 0.9rem;
          color: #666;
        }

        .info-item {
          padding: 1rem;
          background: #f8f9fa;
          border-radius: 8px;
          border: 1px solid #dee2e6;
        }

        .vulnerabilities-list {
          display: flex;
          flex-direction: column;
          gap: 1rem;
        }

        .vulnerability-card {
          border: 1px solid #dee2e6;
          border-radius: 8px;
          overflow: hidden;
        }

        .vulnerability-header {
          background: #f8f9fa;
          padding: 1rem;
          display: flex;
          align-items: center;
          gap: 1rem;
        }

        .severity-badge {
          color: white;
          padding: 0.25rem 0.75rem;
          border-radius: 20px;
          font-size: 0.8rem;
          font-weight: bold;
        }

        .vulnerability-header h4 {
          margin: 0;
          color: #333;
        }

        .vulnerability-details {
          padding: 1rem;
        }

        .vulnerability-details p {
          margin-bottom: 0.5rem;
        }

        .technical-details {
          background: #f8f9fa;
          padding: 1rem;
          border-radius: 4px;
          margin-top: 0.5rem;
        }

        .technical-details code {
          display: block;
          background: #e9ecef;
          padding: 0.5rem;
          border-radius: 4px;
          margin: 0.5rem 0;
          font-family: 'Courier New', monospace;
          word-break: break-all;
        }

        .recommendations-list {
          list-style: none;
          padding: 0;
        }

        .recommendations-list li {
          padding: 0.75rem;
          margin-bottom: 0.5rem;
          background: #f8f9fa;
          border: 1px solid #dee2e6;
          border-left: 4px solid #17a2b8;
          border-radius: 4px;
        }
      `}</style>
    </div>
  )
}

export default WebScanner
