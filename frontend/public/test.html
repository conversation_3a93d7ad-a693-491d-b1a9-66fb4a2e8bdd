<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الاتصال - SecScan Pro</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            white-space: pre-wrap;
        }
        .success {
            border-color: #28a745;
            background: #d4edda;
        }
        .error {
            border-color: #dc3545;
            background: #f8d7da;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 اختبار الاتصال مع SecScan Pro API</h1>
        
        <div>
            <button onclick="testConnection()">اختبار الاتصال الأساسي</button>
            <button onclick="testInfo()">اختبار معلومات النظام</button>
            <button onclick="testPerformance()">اختبار إحصائيات الأداء</button>
            <button onclick="testPassword()">اختبار فحص كلمة مرور</button>
        </div>
        
        <div id="results"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000';
        
        function addResult(title, content, isSuccess = true) {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${isSuccess ? 'success' : 'error'}`;
            resultDiv.innerHTML = `<strong>${title}</strong>\n${content}`;
            resultsDiv.appendChild(resultDiv);
        }
        
        async function testConnection() {
            try {
                const response = await fetch(`${API_BASE}/`);
                const data = await response.json();
                addResult('✅ اختبار الاتصال الأساسي', JSON.stringify(data, null, 2), true);
            } catch (error) {
                addResult('❌ اختبار الاتصال الأساسي', `خطأ: ${error.message}`, false);
            }
        }
        
        async function testInfo() {
            try {
                const response = await fetch(`${API_BASE}/info`);
                const data = await response.json();
                addResult('✅ معلومات النظام', JSON.stringify(data, null, 2), true);
            } catch (error) {
                addResult('❌ معلومات النظام', `خطأ: ${error.message}`, false);
            }
        }
        
        async function testPerformance() {
            try {
                const response = await fetch(`${API_BASE}/performance`);
                const data = await response.json();
                addResult('✅ إحصائيات الأداء', JSON.stringify(data, null, 2), true);
            } catch (error) {
                addResult('❌ إحصائيات الأداء', `خطأ: ${error.message}`, false);
            }
        }
        
        async function testPassword() {
            try {
                const response = await fetch(`${API_BASE}/api/v1/password/analyze`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        password: 'TestPassword123!',
                        include_password_in_response: false
                    })
                });
                const data = await response.json();
                addResult('✅ فحص كلمة مرور', JSON.stringify(data, null, 2), true);
            } catch (error) {
                addResult('❌ فحص كلمة مرور', `خطأ: ${error.message}`, false);
            }
        }
        
        // اختبار تلقائي عند تحميل الصفحة
        window.onload = function() {
            addResult('🚀 بدء الاختبارات', 'جاري اختبار الاتصال مع الخادم...', true);
            setTimeout(testConnection, 1000);
        };
    </script>
</body>
</html>
