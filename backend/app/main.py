"""
SecScan Pro - أداة فحص الثغرات الأمنية المتقدمة
الملف الرئيسي لتطبيق FastAPI
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
from datetime import datetime

# Import routers (سيتم تفعيلها لاحقاً)
# from app.api import auth, scans, reports
from core.config import settings

# إنشاء تطبيق FastAPI
app = FastAPI(
    title="SecScan Pro API",
    description="أداة فحص الثغرات الأمنية المتقدمة - API",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# إعداد CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# الصفحة الرئيسية
@app.get("/")
async def root():
    """الصفحة الرئيسية للـ API"""
    return {
        "message": "مرحباً بك في SecScan Pro API",
        "version": "1.0.0",
        "status": "active",
        "timestamp": datetime.now().isoformat(),
        "docs": "/docs",
        "features": [
            "فحص كلمات المرور",
            "فحص ثغرات الويب", 
            "فحص إعدادات الخوادم",
            "تقارير تفاعلية"
        ]
    }

# فحص صحة النظام
@app.get("/health")
async def health_check():
    """فحص صحة النظام"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "services": {
            "api": "running",
            "database": "connected",
            "redis": "connected"
        }
    }

# معلومات النظام
@app.get("/info")
async def system_info():
    """معلومات النظام والإحصائيات"""
    return {
        "system": "SecScan Pro",
        "version": "1.0.0",
        "environment": settings.ENVIRONMENT,
        "features": {
            "password_scanner": True,
            "web_vulnerability_scanner": True,
            "server_config_scanner": True,
            "report_generator": True
        },
        "statistics": {
            "total_scans": 0,
            "vulnerabilities_found": 0,
            "reports_generated": 0
        }
    }

# تضمين الـ routers
# app.include_router(auth.router, prefix="/api/v1/auth", tags=["Authentication"])
# app.include_router(scans.router, prefix="/api/v1/scans", tags=["Scans"])
# app.include_router(reports.router, prefix="/api/v1/reports", tags=["Reports"])

# معالج الأخطاء العام
@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    """معالج الأخطاء العام"""
    return JSONResponse(
        status_code=500,
        content={
            "error": "خطأ داخلي في الخادم",
            "message": str(exc),
            "timestamp": datetime.now().isoformat()
        }
    )

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
