"""
وحدة فحص ثغرات تطبيقات الويب
اكتشاف الثغرات الأمنية الشائعة في تطبيقات الويب
"""

import requests
import re
import urllib.parse
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import time
import random
from bs4 import BeautifulSoup
from app.core.performance import (
    cache_result, time_execution, ParallelScanner,
    MemoryOptimizer, connection_pool
)

class VulnerabilityType(Enum):
    """أنواع الثغرات الأمنية"""
    SQL_INJECTION = "SQL Injection"
    XSS_REFLECTED = "Reflected XSS"
    XSS_STORED = "Stored XSS"
    CSRF = "Cross-Site Request Forgery"
    DIRECTORY_TRAVERSAL = "Directory Traversal"
    COMMAND_INJECTION = "Command Injection"
    FILE_UPLOAD = "File Upload Vulnerability"
    OPEN_REDIRECT = "Open Redirect"
    XXE = "XML External Entity"
    SSRF = "Server-Side Request Forgery"

class SeverityLevel(Enum):
    """مستويات خطورة الثغرات"""
    LOW = "منخفض"
    MEDIUM = "متوسط"
    HIGH = "عالي"
    CRITICAL = "حرج"

@dataclass
class Vulnerability:
    """معلومات الثغرة المكتشفة"""
    type: VulnerabilityType
    severity: SeverityLevel
    url: str
    parameter: Optional[str]
    payload: str
    evidence: str
    description: str
    impact: str
    remediation: str
    confidence: float  # من 0 إلى 1

@dataclass
class ScanResult:
    """نتائج فحص الموقع"""
    target_url: str
    vulnerabilities: List[Vulnerability]
    scan_duration: float
    pages_scanned: int
    forms_found: int
    parameters_tested: int
    summary: Dict[str, int]

class WebVulnerabilityScanner:
    """فاحص ثغرات تطبيقات الويب"""

    def __init__(self):
        self.session = connection_pool.get_connection()

        # إعدادات الفحص المحسنة
        self.timeout = 5  # تقليل timeout
        self.max_pages = 20  # تقليل عدد الصفحات
        self.delay_between_requests = 0.5  # تقليل التأخير

        # تحميل payloads محسنة
        self.sql_payloads = MemoryOptimizer.optimize_payload_list(
            self._load_sql_payloads(), max_size=50
        )
        self.xss_payloads = MemoryOptimizer.optimize_payload_list(
            self._load_xss_payloads(), max_size=30
        )
        self.traversal_payloads = MemoryOptimizer.optimize_payload_list(
            self._load_traversal_payloads(), max_size=20
        )
        self.command_payloads = MemoryOptimizer.optimize_payload_list(
            self._load_command_payloads(), max_size=30
        )

        # إعداد الفاحص المتوازي
        self.parallel_scanner = ParallelScanner(max_workers=5)

    def _load_sql_payloads(self) -> List[str]:
        """تحميل payloads لفحص SQL Injection"""
        return [
            "' OR '1'='1",
            "' OR 1=1--",
            "' OR 1=1#",
            "' OR 1=1/*",
            "') OR ('1'='1",
            "') OR (1=1)--",
            "' UNION SELECT NULL--",
            "' UNION SELECT 1,2,3--",
            "'; DROP TABLE users--",
            "' AND (SELECT COUNT(*) FROM information_schema.tables)>0--",
            "' AND (SELECT SUBSTRING(@@version,1,1))='5'--",
            "' WAITFOR DELAY '00:00:05'--",
            "'; EXEC xp_cmdshell('ping 127.0.0.1')--"
        ]

    def _load_xss_payloads(self) -> List[str]:
        """تحميل payloads لفحص XSS"""
        return [
            "<script>alert('XSS')</script>",
            "<img src=x onerror=alert('XSS')>",
            "<svg onload=alert('XSS')>",
            "javascript:alert('XSS')",
            "<iframe src=javascript:alert('XSS')>",
            "<body onload=alert('XSS')>",
            "<input onfocus=alert('XSS') autofocus>",
            "<select onfocus=alert('XSS') autofocus>",
            "<textarea onfocus=alert('XSS') autofocus>",
            "<keygen onfocus=alert('XSS') autofocus>",
            "<video><source onerror=alert('XSS')>",
            "<audio src=x onerror=alert('XSS')>",
            "'\"><script>alert('XSS')</script>",
            "</script><script>alert('XSS')</script>",
            "<script>alert(String.fromCharCode(88,83,83))</script>"
        ]

    def _load_traversal_payloads(self) -> List[str]:
        """تحميل payloads لفحص Directory Traversal"""
        return [
            "../../../etc/passwd",
            "..\\..\\..\\windows\\system32\\drivers\\etc\\hosts",
            "....//....//....//etc/passwd",
            "..%2F..%2F..%2Fetc%2Fpasswd",
            "..%252F..%252F..%252Fetc%252Fpasswd",
            "%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd",
            "....\\\\....\\\\....\\\\windows\\\\system32\\\\drivers\\\\etc\\\\hosts",
            "/etc/passwd%00",
            "..%c0%af..%c0%af..%c0%afetc%c0%afpasswd",
            "php://filter/read=convert.base64-encode/resource=index.php"
        ]

    def _load_command_payloads(self) -> List[str]:
        """تحميل payloads لفحص Command Injection"""
        return [
            "; ls -la",
            "| ls -la",
            "&& ls -la",
            "|| ls -la",
            "; dir",
            "| dir",
            "&& dir",
            "|| dir",
            "; cat /etc/passwd",
            "| cat /etc/passwd",
            "; ping -c 4 127.0.0.1",
            "| ping -c 4 127.0.0.1",
            "; whoami",
            "| whoami",
            "`ls -la`",
            "$(ls -la)",
            "${IFS}ls${IFS}-la"
        ]

    @time_execution
    def scan_website(self, target_url: str, max_depth: int = 2) -> ScanResult:
        """فحص شامل لموقع ويب"""
        start_time = time.time()
        vulnerabilities = []
        pages_scanned = 0
        forms_found = 0
        parameters_tested = 0

        try:
            # تطبيع URL
            if not target_url.startswith(('http://', 'https://')):
                target_url = 'http://' + target_url

            # جمع الصفحات والنماذج
            pages = self._crawl_website(target_url, max_depth)
            pages_scanned = len(pages)

            for page_url in pages:
                # فحص الصفحة للثغرات
                page_vulns, forms_count, params_count = self._scan_page(page_url)
                vulnerabilities.extend(page_vulns)
                forms_found += forms_count
                parameters_tested += params_count

                # تأخير بين الطلبات
                time.sleep(self.delay_between_requests)

        except Exception as e:
            print(f"خطأ في فحص الموقع: {e}")

        # حساب الملخص
        summary = self._calculate_summary(vulnerabilities)

        scan_duration = time.time() - start_time

        return ScanResult(
            target_url=target_url,
            vulnerabilities=vulnerabilities,
            scan_duration=scan_duration,
            pages_scanned=pages_scanned,
            forms_found=forms_found,
            parameters_tested=parameters_tested,
            summary=summary
        )

    def _crawl_website(self, base_url: str, max_depth: int) -> List[str]:
        """استكشاف صفحات الموقع"""
        visited = set()
        to_visit = [(base_url, 0)]
        pages = []

        while to_visit and len(pages) < self.max_pages:
            url, depth = to_visit.pop(0)

            if url in visited or depth > max_depth:
                continue

            try:
                response = self.session.get(url, timeout=self.timeout)
                if response.status_code == 200:
                    visited.add(url)
                    pages.append(url)

                    # استخراج الروابط للمستوى التالي
                    if depth < max_depth:
                        soup = BeautifulSoup(response.text, 'html.parser')
                        for link in soup.find_all('a', href=True):
                            href = link['href']
                            full_url = urllib.parse.urljoin(url, href)

                            # فلترة الروابط
                            if self._is_valid_url(full_url, base_url):
                                to_visit.append((full_url, depth + 1))

            except Exception as e:
                print(f"خطأ في استكشاف {url}: {e}")
                continue

        return pages

    def _is_valid_url(self, url: str, base_url: str) -> bool:
        """فحص صحة الرابط"""
        try:
            parsed_url = urllib.parse.urlparse(url)
            parsed_base = urllib.parse.urlparse(base_url)

            # نفس النطاق فقط
            if parsed_url.netloc != parsed_base.netloc:
                return False

            # تجنب الملفات غير المرغوبة
            excluded_extensions = ['.pdf', '.jpg', '.png', '.gif', '.css', '.js', '.ico']
            if any(url.lower().endswith(ext) for ext in excluded_extensions):
                return False

            return True

        except:
            return False

    def _scan_page(self, url: str) -> Tuple[List[Vulnerability], int, int]:
        """فحص صفحة واحدة للثغرات"""
        vulnerabilities = []
        forms_found = 0
        parameters_tested = 0

        try:
            response = self.session.get(url, timeout=self.timeout)
            soup = BeautifulSoup(response.text, 'html.parser')

            # فحص النماذج
            forms = soup.find_all('form')
            forms_found = len(forms)

            for form in forms:
                form_vulns, params_count = self._scan_form(url, form)
                vulnerabilities.extend(form_vulns)
                parameters_tested += params_count

            # فحص معاملات URL
            url_vulns, url_params = self._scan_url_parameters(url)
            vulnerabilities.extend(url_vulns)
            parameters_tested += url_params

            # فحص ثغرات أخرى
            other_vulns = self._scan_other_vulnerabilities(url, response.text)
            vulnerabilities.extend(other_vulns)

        except Exception as e:
            print(f"خطأ في فحص الصفحة {url}: {e}")

        return vulnerabilities, forms_found, parameters_tested

    def _scan_form(self, page_url: str, form) -> Tuple[List[Vulnerability], int]:
        """فحص نموذج للثغرات"""
        vulnerabilities = []
        parameters_tested = 0

        try:
            # استخراج معلومات النموذج
            action = form.get('action', '')
            method = form.get('method', 'get').lower()
            form_url = urllib.parse.urljoin(page_url, action)

            # جمع الحقول
            inputs = form.find_all(['input', 'textarea', 'select'])
            form_data = {}

            for input_field in inputs:
                name = input_field.get('name')
                if name:
                    input_type = input_field.get('type', 'text')
                    if input_type not in ['submit', 'button', 'reset']:
                        form_data[name] = 'test_value'
                        parameters_tested += 1

            if form_data:
                # فحص SQL Injection
                sql_vulns = self._test_sql_injection(form_url, form_data, method)
                vulnerabilities.extend(sql_vulns)

                # فحص XSS
                xss_vulns = self._test_xss(form_url, form_data, method)
                vulnerabilities.extend(xss_vulns)

                # فحص Directory Traversal
                traversal_vulns = self._test_directory_traversal(form_url, form_data, method)
                vulnerabilities.extend(traversal_vulns)

                # فحص Command Injection
                cmd_vulns = self._test_command_injection(form_url, form_data, method)
                vulnerabilities.extend(cmd_vulns)

        except Exception as e:
            print(f"خطأ في فحص النموذج: {e}")

        return vulnerabilities, parameters_tested

    def _test_sql_injection(self, url: str, data: Dict, method: str) -> List[Vulnerability]:
        """اختبار SQL Injection"""
        vulnerabilities = []

        for param_name in data.keys():
            for payload in self.sql_payloads:
                try:
                    test_data = data.copy()
                    test_data[param_name] = payload

                    if method == 'post':
                        response = self.session.post(url, data=test_data, timeout=self.timeout)
                    else:
                        response = self.session.get(url, params=test_data, timeout=self.timeout)

                    # فحص علامات SQL Injection
                    if self._detect_sql_injection(response.text, payload):
                        vulnerability = Vulnerability(
                            type=VulnerabilityType.SQL_INJECTION,
                            severity=SeverityLevel.HIGH,
                            url=url,
                            parameter=param_name,
                            payload=payload,
                            evidence=self._extract_evidence(response.text, payload),
                            description=f"تم اكتشاف ثغرة SQL Injection في المعامل {param_name}",
                            impact="يمكن للمهاجم الوصول إلى قاعدة البيانات وسرقة أو تعديل البيانات",
                            remediation="استخدم Prepared Statements وتحقق من صحة المدخلات",
                            confidence=0.8
                        )
                        vulnerabilities.append(vulnerability)
                        break  # توقف عند أول ثغرة مكتشفة لهذا المعامل

                except Exception as e:
                    continue

        return vulnerabilities

    def _test_xss(self, url: str, data: Dict, method: str) -> List[Vulnerability]:
        """اختبار XSS"""
        vulnerabilities = []

        for param_name in data.keys():
            for payload in self.xss_payloads:
                try:
                    test_data = data.copy()
                    test_data[param_name] = payload

                    if method == 'post':
                        response = self.session.post(url, data=test_data, timeout=self.timeout)
                    else:
                        response = self.session.get(url, params=test_data, timeout=self.timeout)

                    # فحص علامات XSS
                    if self._detect_xss(response.text, payload):
                        vulnerability = Vulnerability(
                            type=VulnerabilityType.XSS_REFLECTED,
                            severity=SeverityLevel.MEDIUM,
                            url=url,
                            parameter=param_name,
                            payload=payload,
                            evidence=self._extract_evidence(response.text, payload),
                            description=f"تم اكتشاف ثغرة XSS في المعامل {param_name}",
                            impact="يمكن للمهاجم تنفيذ أكواد JavaScript في متصفح الضحية",
                            remediation="قم بتنظيف وترميز جميع المدخلات قبل عرضها",
                            confidence=0.9
                        )
                        vulnerabilities.append(vulnerability)
                        break

                except Exception as e:
                    continue

        return vulnerabilities

    def _test_directory_traversal(self, url: str, data: Dict, method: str) -> List[Vulnerability]:
        """اختبار Directory Traversal"""
        vulnerabilities = []

        for param_name in data.keys():
            for payload in self.traversal_payloads:
                try:
                    test_data = data.copy()
                    test_data[param_name] = payload

                    if method == 'post':
                        response = self.session.post(url, data=test_data, timeout=self.timeout)
                    else:
                        response = self.session.get(url, params=test_data, timeout=self.timeout)

                    # فحص علامات Directory Traversal
                    if self._detect_directory_traversal(response.text, payload):
                        vulnerability = Vulnerability(
                            type=VulnerabilityType.DIRECTORY_TRAVERSAL,
                            severity=SeverityLevel.HIGH,
                            url=url,
                            parameter=param_name,
                            payload=payload,
                            evidence=self._extract_evidence(response.text, payload),
                            description=f"تم اكتشاف ثغرة Directory Traversal في المعامل {param_name}",
                            impact="يمكن للمهاجم الوصول إلى ملفات النظام الحساسة",
                            remediation="تحقق من صحة مسارات الملفات وقم بتقييد الوصول",
                            confidence=0.7
                        )
                        vulnerabilities.append(vulnerability)
                        break

                except Exception as e:
                    continue

        return vulnerabilities

    def _test_command_injection(self, url: str, data: Dict, method: str) -> List[Vulnerability]:
        """اختبار Command Injection"""
        vulnerabilities = []

        for param_name in data.keys():
            for payload in self.command_payloads:
                try:
                    test_data = data.copy()
                    test_data[param_name] = payload

                    if method == 'post':
                        response = self.session.post(url, data=test_data, timeout=self.timeout)
                    else:
                        response = self.session.get(url, params=test_data, timeout=self.timeout)

                    # فحص علامات Command Injection
                    if self._detect_command_injection(response.text, payload):
                        vulnerability = Vulnerability(
                            type=VulnerabilityType.COMMAND_INJECTION,
                            severity=SeverityLevel.CRITICAL,
                            url=url,
                            parameter=param_name,
                            payload=payload,
                            evidence=self._extract_evidence(response.text, payload),
                            description=f"تم اكتشاف ثغرة Command Injection في المعامل {param_name}",
                            impact="يمكن للمهاجم تنفيذ أوامر النظام والسيطرة على الخادم",
                            remediation="تجنب تنفيذ أوامر النظام مع مدخلات المستخدم",
                            confidence=0.8
                        )
                        vulnerabilities.append(vulnerability)
                        break

                except Exception as e:
                    continue

        return vulnerabilities

    def _detect_sql_injection(self, response_text: str, payload: str) -> bool:
        """كشف علامات SQL Injection"""
        sql_errors = [
            "mysql_fetch_array",
            "ORA-01756",
            "Microsoft OLE DB Provider for ODBC Drivers",
            "PostgreSQL query failed",
            "Warning: mysql_",
            "valid MySQL result",
            "MySqlClient.",
            "PostgreSQL.*ERROR",
            "Warning.*\\Wmysql_.*",
            "valid MySQL result",
            "ORA-\\d{5}",
            "Oracle error",
            "Oracle.*Driver",
            "Warning.*\\Woci_.*",
            "Warning.*\\Wpg_.*",
            "valid PostgreSQL result",
            "Npgsql\\.",
            "PG::SyntaxError:",
            "org.postgresql.util.PSQLException",
            "ERROR:\\s\\ssyntax error at or near",
            "ERROR: parser: parse error at or near",
            "PostgreSQL query failed: ERROR: parser: parse error",
            "syntax error at line",
            "OLE DB.*SQL Server",
            "\\bSQL Server.*Driver",
            "Warning.*\\Wmssql_.*",
            "\\bSQL Server.*[0-9a-fA-F]{8}",
            "Exception.*\\WSystem\\.Data\\.SqlClient\\.",
            "Exception.*\\WRoadhouse\\.Cms\\.",
            "Microsoft SQL Native Client error '[0-9a-fA-F]{8}",
            "\\[SQL Server\\]",
            "ODBC SQL Server Driver",
            "ODBC Driver.*SQL Server",
            "SQLServer JDBC Driver",
            "SqlException"
        ]

        response_lower = response_text.lower()
        for error in sql_errors:
            if re.search(error.lower(), response_lower):
                return True

        # فحص تأخير الاستجابة للـ time-based injection
        if "waitfor" in payload.lower() and len(response_text) > 0:
            return True

        return False

    def _detect_xss(self, response_text: str, payload: str) -> bool:
        """كشف علامات XSS"""
        # فحص وجود payload الخام في الاستجابة (ثغرة مؤكدة)
        if payload in response_text:
            return True

        # فحص payload مرمز جزئياً (قد يكون قابل للاستغلال)
        import html
        encoded_payload = html.escape(payload)
        if encoded_payload in response_text:
            # إذا كان payload مرمز بالكامل، فهذا آمن
            return False

        # فحص أجزاء من payload
        dangerous_parts = ['<script', 'javascript:', 'onerror=', 'onload=', 'alert(']
        for part in dangerous_parts:
            if part.lower() in response_text.lower():
                return True

        return False

    def _detect_directory_traversal(self, response_text: str, payload: str) -> bool:
        """كشف علامات Directory Traversal"""
        traversal_indicators = [
            "root:x:0:0:",
            "daemon:x:1:1:",
            "bin:x:2:2:",
            "sys:x:3:3:",
            "# localhost name resolution is handled within DNS itself.",
            "# 127.0.0.1       localhost",
            "# ::1             localhost",
            "[boot loader]",
            "[operating systems]",
            "<?php",
            "<?xml",
            "<!DOCTYPE html"
        ]

        response_lower = response_text.lower()
        for indicator in traversal_indicators:
            if indicator.lower() in response_lower:
                return True

        return False

    def _detect_command_injection(self, response_text: str, payload: str) -> bool:
        """كشف علامات Command Injection"""
        command_indicators = [
            "uid=",
            "gid=",
            "groups=",
            "total ",
            "drwx",
            "-rw-",
            "Volume in drive",
            "Directory of",
            "PING",
            "64 bytes from",
            "packets transmitted",
            "packet loss"
        ]

        for indicator in command_indicators:
            if indicator in response_text:
                return True

        return False

    def _extract_evidence(self, response_text: str, payload: str) -> str:
        """استخراج دليل الثغرة من الاستجابة"""
        # البحث عن payload في الاستجابة
        if payload in response_text:
            # العثور على السياق المحيط بـ payload
            index = response_text.find(payload)
            start = max(0, index - 100)
            end = min(len(response_text), index + len(payload) + 100)
            return response_text[start:end].strip()

        # إذا لم يوجد payload، أعطي جزء من الاستجابة
        return response_text[:200] + "..." if len(response_text) > 200 else response_text

    def _scan_url_parameters(self, url: str) -> Tuple[List[Vulnerability], int]:
        """فحص معاملات URL للثغرات"""
        vulnerabilities = []
        parameters_tested = 0

        try:
            parsed_url = urllib.parse.urlparse(url)
            if parsed_url.query:
                params = urllib.parse.parse_qs(parsed_url.query)
                parameters_tested = len(params)

                # تحويل إلى تنسيق مناسب للاختبار
                test_params = {k: v[0] if v else '' for k, v in params.items()}

                if test_params:
                    # اختبار الثغرات على معاملات URL
                    base_url = f"{parsed_url.scheme}://{parsed_url.netloc}{parsed_url.path}"

                    sql_vulns = self._test_sql_injection(base_url, test_params, 'get')
                    vulnerabilities.extend(sql_vulns)

                    xss_vulns = self._test_xss(base_url, test_params, 'get')
                    vulnerabilities.extend(xss_vulns)

                    traversal_vulns = self._test_directory_traversal(base_url, test_params, 'get')
                    vulnerabilities.extend(traversal_vulns)

        except Exception as e:
            print(f"خطأ في فحص معاملات URL: {e}")

        return vulnerabilities, parameters_tested

    def _scan_other_vulnerabilities(self, url: str, response_text: str) -> List[Vulnerability]:
        """فحص ثغرات أخرى"""
        vulnerabilities = []

        try:
            # فحص CSRF
            csrf_vuln = self._check_csrf_protection(url, response_text)
            if csrf_vuln:
                vulnerabilities.append(csrf_vuln)

            # فحص Open Redirect
            redirect_vulns = self._check_open_redirect(url)
            vulnerabilities.extend(redirect_vulns)

            # فحص معلومات حساسة في الاستجابة
            info_vulns = self._check_information_disclosure(url, response_text)
            vulnerabilities.extend(info_vulns)

        except Exception as e:
            print(f"خطأ في فحص ثغرات أخرى: {e}")

        return vulnerabilities

    def _check_csrf_protection(self, url: str, response_text: str) -> Optional[Vulnerability]:
        """فحص حماية CSRF"""
        # البحث عن CSRF tokens
        csrf_patterns = [
            r'<input[^>]*name=["\']_token["\'][^>]*>',
            r'<input[^>]*name=["\']csrf_token["\'][^>]*>',
            r'<input[^>]*name=["\']authenticity_token["\'][^>]*>',
            r'<meta[^>]*name=["\']csrf-token["\'][^>]*>',
        ]

        has_csrf_protection = False
        for pattern in csrf_patterns:
            if re.search(pattern, response_text, re.IGNORECASE):
                has_csrf_protection = True
                break

        if not has_csrf_protection:
            # فحص وجود نماذج POST
            if re.search(r'<form[^>]*method=["\']post["\'][^>]*>', response_text, re.IGNORECASE):
                return Vulnerability(
                    type=VulnerabilityType.CSRF,
                    severity=SeverityLevel.MEDIUM,
                    url=url,
                    parameter=None,
                    payload="",
                    evidence="لا توجد حماية CSRF في النماذج",
                    description="النماذج لا تحتوي على حماية CSRF",
                    impact="يمكن للمهاجم تنفيذ طلبات غير مرغوبة باسم المستخدم",
                    remediation="أضف CSRF tokens لجميع النماذج الحساسة",
                    confidence=0.6
                )

        return None

    def _check_open_redirect(self, url: str) -> List[Vulnerability]:
        """فحص Open Redirect"""
        vulnerabilities = []

        redirect_params = ['redirect', 'url', 'next', 'return', 'goto', 'continue']
        malicious_url = "http://evil.com"

        try:
            parsed_url = urllib.parse.urlparse(url)

            for param in redirect_params:
                test_url = f"{url}{'&' if parsed_url.query else '?'}{param}={malicious_url}"

                response = self.session.get(test_url, timeout=self.timeout, allow_redirects=False)

                if response.status_code in [301, 302, 303, 307, 308]:
                    location = response.headers.get('Location', '')
                    if malicious_url in location:
                        vulnerability = Vulnerability(
                            type=VulnerabilityType.OPEN_REDIRECT,
                            severity=SeverityLevel.LOW,
                            url=url,
                            parameter=param,
                            payload=malicious_url,
                            evidence=f"Location: {location}",
                            description=f"تم اكتشاف ثغرة Open Redirect في المعامل {param}",
                            impact="يمكن للمهاجم إعادة توجيه المستخدمين إلى مواقع ضارة",
                            remediation="تحقق من صحة URLs المعاد التوجيه إليها",
                            confidence=0.8
                        )
                        vulnerabilities.append(vulnerability)

        except Exception as e:
            pass

        return vulnerabilities

    def _check_information_disclosure(self, url: str, response_text: str) -> List[Vulnerability]:
        """فحص تسريب المعلومات"""
        vulnerabilities = []

        # أنماط المعلومات الحساسة
        sensitive_patterns = [
            (r'password\s*[:=]\s*["\'][^"\']+["\']', "كلمات مرور مكشوفة"),
            (r'api[_-]?key\s*[:=]\s*["\'][^"\']+["\']', "مفاتيح API مكشوفة"),
            (r'secret[_-]?key\s*[:=]\s*["\'][^"\']+["\']', "مفاتيح سرية مكشوفة"),
            (r'mysql://[^"\s]+', "سلاسل اتصال قاعدة البيانات"),
            (r'postgresql://[^"\s]+', "سلاسل اتصال قاعدة البيانات"),
            (r'mongodb://[^"\s]+', "سلاسل اتصال قاعدة البيانات"),
            (r'(?i)error|exception|stack\s+trace', "رسائل خطأ مفصلة"),
        ]

        for pattern, description in sensitive_patterns:
            matches = re.findall(pattern, response_text, re.IGNORECASE)
            if matches:
                vulnerability = Vulnerability(
                    type=VulnerabilityType.OPEN_REDIRECT,  # استخدام نوع عام
                    severity=SeverityLevel.MEDIUM,
                    url=url,
                    parameter=None,
                    payload="",
                    evidence=str(matches[0])[:100],
                    description=f"تسريب معلومات: {description}",
                    impact="تسريب معلومات حساسة يمكن أن يساعد المهاجمين",
                    remediation="أزل المعلومات الحساسة من الاستجابات",
                    confidence=0.7
                )
                vulnerabilities.append(vulnerability)

        return vulnerabilities

    def _calculate_summary(self, vulnerabilities: List[Vulnerability]) -> Dict[str, int]:
        """حساب ملخص الثغرات"""
        summary = {
            "total": len(vulnerabilities),
            "critical": 0,
            "high": 0,
            "medium": 0,
            "low": 0
        }

        for vuln in vulnerabilities:
            if vuln.severity == SeverityLevel.CRITICAL:
                summary["critical"] += 1
            elif vuln.severity == SeverityLevel.HIGH:
                summary["high"] += 1
            elif vuln.severity == SeverityLevel.MEDIUM:
                summary["medium"] += 1
            elif vuln.severity == SeverityLevel.LOW:
                summary["low"] += 1

        return summary
