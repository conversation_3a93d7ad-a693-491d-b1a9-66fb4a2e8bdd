"""
وحدة فحص إعدادات الخوادم
فحص إعدادات الأمان للخوادم والبروتوكولات
"""

import socket
import ssl
import requests
import subprocess
import re
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
from datetime import datetime, timedelta
import urllib.parse
import json
import concurrent.futures
from app.core.performance import (
    cache_result, time_execution, ParallelScanner,
    connection_pool
)

class ServerIssueType(Enum):
    """أنواع مشاكل الخادم"""
    SSL_WEAK_CIPHER = "SSL Weak Cipher"
    SSL_EXPIRED_CERT = "SSL Expired Certificate"
    SSL_SELF_SIGNED = "SSL Self-Signed Certificate"
    MISSING_SECURITY_HEADER = "Missing Security Header"
    INFORMATION_DISCLOSURE = "Information Disclosure"
    WEAK_SSL_VERSION = "Weak SSL Version"
    OPEN_PORT = "Open Port"
    INSECURE_SERVICE = "Insecure Service"
    WEAK_CONFIGURATION = "Weak Configuration"

class ServerSeverity(Enum):
    """مستويات خطورة مشاكل الخادم"""
    INFO = "معلوماتي"
    LOW = "منخفض"
    MEDIUM = "متوسط"
    HIGH = "عالي"
    CRITICAL = "حرج"

@dataclass
class ServerIssue:
    """مشكلة في إعدادات الخادم"""
    type: ServerIssueType
    severity: ServerSeverity
    title: str
    description: str
    evidence: str
    impact: str
    remediation: str
    port: Optional[int] = None
    service: Optional[str] = None

@dataclass
class SSLInfo:
    """معلومات شهادة SSL"""
    version: str
    cipher: str
    certificate_valid: bool
    certificate_expired: bool
    certificate_self_signed: bool
    certificate_issuer: str
    certificate_subject: str
    certificate_expiry: Optional[datetime]
    supported_protocols: List[str]
    weak_ciphers: List[str]

@dataclass
class SecurityHeaders:
    """رؤوس الأمان HTTP"""
    strict_transport_security: Optional[str] = None
    content_security_policy: Optional[str] = None
    x_frame_options: Optional[str] = None
    x_content_type_options: Optional[str] = None
    x_xss_protection: Optional[str] = None
    referrer_policy: Optional[str] = None
    permissions_policy: Optional[str] = None

@dataclass
class ServerScanResult:
    """نتائج فحص الخادم"""
    target: str
    ip_address: str
    open_ports: List[int]
    services: Dict[int, str]
    ssl_info: Optional[SSLInfo]
    security_headers: SecurityHeaders
    server_info: Dict[str, str]
    issues: List[ServerIssue]
    scan_duration: float
    summary: Dict[str, int]

class ServerConfigScanner:
    """فاحص إعدادات الخوادم"""

    def __init__(self):
        self.timeout = 5  # تقليل timeout
        self.common_ports = [22, 80, 443, 8080, 8443]  # تقليل البورتات للفحص السريع
        self.weak_ssl_versions = ['SSLv2', 'SSLv3', 'TLSv1.0', 'TLSv1.1']
        self.weak_ciphers = [
            'RC4', 'DES', '3DES', 'MD5', 'SHA1', 'NULL', 'EXPORT', 'ADH', 'AECDH'
        ]
        # إعداد الفاحص المتوازي
        self.parallel_scanner = ParallelScanner(max_workers=10)

    @time_execution
    def scan_server(self, target: str, port_scan: bool = True, ssl_scan: bool = True,
                   header_scan: bool = True) -> ServerScanResult:
        """فحص شامل للخادم"""
        start_time = datetime.now()

        # تحليل الهدف
        parsed_target = self._parse_target(target)
        ip_address = self._resolve_hostname(parsed_target['hostname'])

        # فحص البورتات
        open_ports = []
        services = {}
        if port_scan:
            open_ports = self._scan_ports(ip_address)
            services = self._detect_services(ip_address, open_ports)

        # فحص SSL/TLS
        ssl_info = None
        if ssl_scan and (443 in open_ports or parsed_target['port'] == 443):
            ssl_info = self._scan_ssl(parsed_target['hostname'], parsed_target['port'] or 443)

        # فحص رؤوس الأمان
        security_headers = SecurityHeaders()
        server_info = {}
        if header_scan:
            security_headers, server_info = self._scan_headers(target)

        # تجميع المشاكل
        issues = []
        if ssl_info:
            issues.extend(self._analyze_ssl_issues(ssl_info))
        issues.extend(self._analyze_header_issues(security_headers))
        issues.extend(self._analyze_port_issues(open_ports, services))
        issues.extend(self._analyze_server_info_issues(server_info))

        # حساب الملخص
        summary = self._calculate_summary(issues)

        scan_duration = (datetime.now() - start_time).total_seconds()

        return ServerScanResult(
            target=target,
            ip_address=ip_address,
            open_ports=open_ports,
            services=services,
            ssl_info=ssl_info,
            security_headers=security_headers,
            server_info=server_info,
            issues=issues,
            scan_duration=scan_duration,
            summary=summary
        )

    def _parse_target(self, target: str) -> Dict[str, Optional[str]]:
        """تحليل الهدف المدخل"""
        if not target.startswith(('http://', 'https://')):
            target = 'https://' + target

        parsed = urllib.parse.urlparse(target)
        return {
            'hostname': parsed.hostname,
            'port': parsed.port,
            'scheme': parsed.scheme
        }

    def _resolve_hostname(self, hostname: str) -> str:
        """تحويل اسم النطاق إلى IP"""
        try:
            return socket.gethostbyname(hostname)
        except socket.gaierror:
            return hostname

    def _scan_ports(self, ip_address: str) -> List[int]:
        """فحص البورتات المفتوحة"""
        open_ports = []

        for port in self.common_ports:
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(2)
                result = sock.connect_ex((ip_address, port))
                if result == 0:
                    open_ports.append(port)
                sock.close()
            except Exception:
                continue

        return open_ports

    def _detect_services(self, ip_address: str, ports: List[int]) -> Dict[int, str]:
        """تحديد الخدمات المشغلة على البورتات"""
        services = {}

        service_map = {
            21: "FTP",
            22: "SSH",
            23: "Telnet",
            25: "SMTP",
            53: "DNS",
            80: "HTTP",
            110: "POP3",
            143: "IMAP",
            443: "HTTPS",
            993: "IMAPS",
            995: "POP3S",
            8080: "HTTP-Alt",
            8443: "HTTPS-Alt"
        }

        for port in ports:
            if port in service_map:
                services[port] = service_map[port]
            else:
                # محاولة تحديد الخدمة
                service = self._probe_service(ip_address, port)
                services[port] = service or "Unknown"

        return services

    def _probe_service(self, ip_address: str, port: int) -> Optional[str]:
        """فحص نوع الخدمة على بورت معين"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(3)
            sock.connect((ip_address, port))

            # إرسال طلب HTTP بسيط
            sock.send(b"GET / HTTP/1.0\r\n\r\n")
            response = sock.recv(1024).decode('utf-8', errors='ignore')
            sock.close()

            if 'HTTP/' in response:
                return "HTTP"
            elif 'SSH' in response:
                return "SSH"
            elif 'FTP' in response:
                return "FTP"

        except Exception:
            pass

        return None

    def _scan_ssl(self, hostname: str, port: int) -> SSLInfo:
        """فحص إعدادات SSL/TLS"""
        try:
            # إنشاء اتصال SSL
            context = ssl.create_default_context()
            context.check_hostname = False
            context.verify_mode = ssl.CERT_NONE

            with socket.create_connection((hostname, port), timeout=self.timeout) as sock:
                with context.wrap_socket(sock, server_hostname=hostname) as ssock:
                    cert = ssock.getpeercert()
                    cipher = ssock.cipher()
                    version = ssock.version()

                    # تحليل الشهادة
                    cert_valid = True
                    cert_expired = False
                    cert_self_signed = False
                    cert_issuer = ""
                    cert_subject = ""
                    cert_expiry = None

                    if cert:
                        # فحص انتهاء الصلاحية
                        not_after = cert.get('notAfter')
                        if not_after:
                            cert_expiry = datetime.strptime(not_after, '%b %d %H:%M:%S %Y %Z')
                            cert_expired = cert_expiry < datetime.now()

                        # معلومات الشهادة
                        cert_issuer = dict(x[0] for x in cert.get('issuer', []))
                        cert_subject = dict(x[0] for x in cert.get('subject', []))

                        # فحص الشهادة الموقعة ذاتياً
                        if cert_issuer == cert_subject:
                            cert_self_signed = True

                    # فحص البروتوكولات المدعومة
                    supported_protocols = self._test_ssl_protocols(hostname, port)

                    # فحص التشفير الضعيف
                    weak_ciphers = []
                    if cipher:
                        cipher_name = cipher[0]
                        for weak in self.weak_ciphers:
                            if weak.lower() in cipher_name.lower():
                                weak_ciphers.append(weak)

                    return SSLInfo(
                        version=version or "Unknown",
                        cipher=cipher[0] if cipher else "Unknown",
                        certificate_valid=cert_valid,
                        certificate_expired=cert_expired,
                        certificate_self_signed=cert_self_signed,
                        certificate_issuer=str(cert_issuer),
                        certificate_subject=str(cert_subject),
                        certificate_expiry=cert_expiry,
                        supported_protocols=supported_protocols,
                        weak_ciphers=weak_ciphers
                    )

        except Exception as e:
            return SSLInfo(
                version="Error",
                cipher="Error",
                certificate_valid=False,
                certificate_expired=True,
                certificate_self_signed=False,
                certificate_issuer=f"Error: {str(e)}",
                certificate_subject="",
                certificate_expiry=None,
                supported_protocols=[],
                weak_ciphers=[]
            )

    def _test_ssl_protocols(self, hostname: str, port: int) -> List[str]:
        """اختبار البروتوكولات المدعومة"""
        supported = []
        protocols = [
            ('SSLv2', ssl.PROTOCOL_SSLv23),
            ('SSLv3', ssl.PROTOCOL_SSLv23),
            ('TLSv1.0', ssl.PROTOCOL_TLSv1),
            ('TLSv1.1', ssl.PROTOCOL_TLSv1_1),
            ('TLSv1.2', ssl.PROTOCOL_TLSv1_2),
        ]

        # إضافة TLSv1.3 إذا كان متاحاً
        if hasattr(ssl, 'PROTOCOL_TLSv1_3'):
            protocols.append(('TLSv1.3', ssl.PROTOCOL_TLSv1_3))

        for protocol_name, protocol_const in protocols:
            try:
                context = ssl.SSLContext(protocol_const)
                context.check_hostname = False
                context.verify_mode = ssl.CERT_NONE

                with socket.create_connection((hostname, port), timeout=5) as sock:
                    with context.wrap_socket(sock) as ssock:
                        supported.append(protocol_name)
            except Exception:
                continue

        return supported

    def _scan_headers(self, target: str) -> Tuple[SecurityHeaders, Dict[str, str]]:
        """فحص رؤوس الأمان HTTP"""
        try:
            response = requests.get(target, timeout=self.timeout, verify=False)
            headers = response.headers

            security_headers = SecurityHeaders(
                strict_transport_security=headers.get('Strict-Transport-Security'),
                content_security_policy=headers.get('Content-Security-Policy'),
                x_frame_options=headers.get('X-Frame-Options'),
                x_content_type_options=headers.get('X-Content-Type-Options'),
                x_xss_protection=headers.get('X-XSS-Protection'),
                referrer_policy=headers.get('Referrer-Policy'),
                permissions_policy=headers.get('Permissions-Policy')
            )

            server_info = {
                'server': headers.get('Server', ''),
                'x_powered_by': headers.get('X-Powered-By', ''),
                'x_aspnet_version': headers.get('X-AspNet-Version', ''),
                'x_generator': headers.get('X-Generator', ''),
                'status_code': str(response.status_code)
            }

            return security_headers, server_info

        except Exception as e:
            return SecurityHeaders(), {'error': str(e)}

    def _analyze_ssl_issues(self, ssl_info: SSLInfo) -> List[ServerIssue]:
        """تحليل مشاكل SSL/TLS"""
        issues = []

        # شهادة منتهية الصلاحية
        if ssl_info.certificate_expired:
            issues.append(ServerIssue(
                type=ServerIssueType.SSL_EXPIRED_CERT,
                severity=ServerSeverity.HIGH,
                title="شهادة SSL منتهية الصلاحية",
                description="شهادة SSL/TLS منتهية الصلاحية",
                evidence=f"تاريخ انتهاء الصلاحية: {ssl_info.certificate_expiry}",
                impact="المستخدمون سيحصلون على تحذيرات أمنية",
                remediation="قم بتجديد شهادة SSL/TLS"
            ))

        # شهادة موقعة ذاتياً
        if ssl_info.certificate_self_signed:
            issues.append(ServerIssue(
                type=ServerIssueType.SSL_SELF_SIGNED,
                severity=ServerSeverity.MEDIUM,
                title="شهادة SSL موقعة ذاتياً",
                description="الشهادة موقعة ذاتياً وليس من جهة موثوقة",
                evidence=f"المُصدر: {ssl_info.certificate_issuer}",
                impact="المستخدمون سيحصلون على تحذيرات أمنية",
                remediation="احصل على شهادة من جهة إصدار موثوقة"
            ))

        # بروتوكولات ضعيفة
        for protocol in ssl_info.supported_protocols:
            if protocol in self.weak_ssl_versions:
                issues.append(ServerIssue(
                    type=ServerIssueType.WEAK_SSL_VERSION,
                    severity=ServerSeverity.HIGH,
                    title=f"بروتوكول SSL/TLS ضعيف: {protocol}",
                    description=f"الخادم يدعم بروتوكول {protocol} الضعيف",
                    evidence=f"البروتوكول المدعوم: {protocol}",
                    impact="عرضة لهجمات التشفير المعروفة",
                    remediation=f"قم بتعطيل {protocol} واستخدم TLS 1.2+ فقط"
                ))

        # تشفير ضعيف
        for weak_cipher in ssl_info.weak_ciphers:
            issues.append(ServerIssue(
                type=ServerIssueType.SSL_WEAK_CIPHER,
                severity=ServerSeverity.MEDIUM,
                title=f"تشفير ضعيف: {weak_cipher}",
                description=f"الخادم يستخدم تشفير {weak_cipher} الضعيف",
                evidence=f"التشفير المستخدم: {ssl_info.cipher}",
                impact="عرضة لهجمات كسر التشفير",
                remediation="استخدم تشفير قوي مثل AES"
            ))

        return issues

    def _analyze_header_issues(self, headers: SecurityHeaders) -> List[ServerIssue]:
        """تحليل مشاكل رؤوس الأمان"""
        issues = []

        # فحص الرؤوس المفقودة
        header_checks = [
            ('strict_transport_security', 'Strict-Transport-Security',
             'يفرض استخدام HTTPS', 'أضف HSTS header'),
            ('content_security_policy', 'Content-Security-Policy',
             'يمنع هجمات XSS وحقن المحتوى', 'أضف CSP header'),
            ('x_frame_options', 'X-Frame-Options',
             'يمنع هجمات Clickjacking', 'أضف X-Frame-Options header'),
            ('x_content_type_options', 'X-Content-Type-Options',
             'يمنع MIME type sniffing', 'أضف X-Content-Type-Options: nosniff'),
            ('x_xss_protection', 'X-XSS-Protection',
             'يفعل حماية XSS في المتصفح', 'أضف X-XSS-Protection: 1; mode=block')
        ]

        for attr, header_name, impact, remediation in header_checks:
            if not getattr(headers, attr):
                issues.append(ServerIssue(
                    type=ServerIssueType.MISSING_SECURITY_HEADER,
                    severity=ServerSeverity.MEDIUM,
                    title=f"رأس الأمان مفقود: {header_name}",
                    description=f"رأس الأمان {header_name} غير موجود",
                    evidence=f"Header {header_name} غير موجود في الاستجابة",
                    impact=impact,
                    remediation=remediation
                ))

        return issues

    def _analyze_port_issues(self, open_ports: List[int], services: Dict[int, str]) -> List[ServerIssue]:
        """تحليل مشاكل البورتات المفتوحة"""
        issues = []

        # بورتات خطيرة
        dangerous_ports = {
            21: ("FTP", "بروتوكول غير مشفر"),
            23: ("Telnet", "بروتوكول غير مشفر وغير آمن"),
            25: ("SMTP", "قد يكون مفتوح للإساءة"),
            53: ("DNS", "قد يكون عرضة لهجمات التضخيم"),
            110: ("POP3", "بروتوكول غير مشفر"),
            143: ("IMAP", "بروتوكول غير مشفر")
        }

        for port in open_ports:
            if port in dangerous_ports:
                service_name, risk = dangerous_ports[port]
                issues.append(ServerIssue(
                    type=ServerIssueType.INSECURE_SERVICE,
                    severity=ServerSeverity.MEDIUM,
                    title=f"خدمة غير آمنة: {service_name} على البورت {port}",
                    description=f"البورت {port} مفتوح ويشغل خدمة {service_name}",
                    evidence=f"البورت {port} مفتوح - الخدمة: {services.get(port, 'Unknown')}",
                    impact=risk,
                    remediation=f"أغلق البورت {port} أو استخدم النسخة المشفرة",
                    port=port,
                    service=service_name
                ))

        return issues

    def _analyze_server_info_issues(self, server_info: Dict[str, str]) -> List[ServerIssue]:
        """تحليل مشاكل معلومات الخادم"""
        issues = []

        # كشف معلومات الخادم
        sensitive_headers = ['server', 'x_powered_by', 'x_aspnet_version', 'x_generator']

        for header in sensitive_headers:
            value = server_info.get(header, '')
            if value and value.strip():
                issues.append(ServerIssue(
                    type=ServerIssueType.INFORMATION_DISCLOSURE,
                    severity=ServerSeverity.LOW,
                    title=f"كشف معلومات الخادم: {header}",
                    description=f"الخادم يكشف معلومات حساسة في رأس {header}",
                    evidence=f"{header}: {value}",
                    impact="يساعد المهاجمين في تحديد نقاط الضعف المحتملة",
                    remediation=f"أخف أو أزل رأس {header}"
                ))

        return issues

    def _calculate_summary(self, issues: List[ServerIssue]) -> Dict[str, int]:
        """حساب ملخص المشاكل"""
        summary = {
            "total": len(issues),
            "critical": 0,
            "high": 0,
            "medium": 0,
            "low": 0,
            "info": 0
        }

        for issue in issues:
            if issue.severity == ServerSeverity.CRITICAL:
                summary["critical"] += 1
            elif issue.severity == ServerSeverity.HIGH:
                summary["high"] += 1
            elif issue.severity == ServerSeverity.MEDIUM:
                summary["medium"] += 1
            elif issue.severity == ServerSeverity.LOW:
                summary["low"] += 1
            elif issue.severity == ServerSeverity.INFO:
                summary["info"] += 1

        return summary
