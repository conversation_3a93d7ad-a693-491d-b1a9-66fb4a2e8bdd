"""
وحدة فحص كلمات المرور
تحليل قوة كلمات المرور واكتشاف الأنماط الضعيفة
"""

import re
import math
import string
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from enum import Enum
import hashlib
from app.core.performance import cache_result, time_execution, MemoryOptimizer

class PasswordStrength(Enum):
    """مستويات قوة كلمة المرور"""
    VERY_WEAK = "ضعيف جداً"
    WEAK = "ضعيف"
    MODERATE = "متوسط"
    STRONG = "قوي"
    VERY_STRONG = "قوي جداً"

@dataclass
class PasswordAnalysis:
    """نتائج تحليل كلمة المرور"""
    password: str
    strength: PasswordStrength
    score: int  # من 0 إلى 100
    entropy: float
    length: int
    character_sets: Dict[str, bool]
    patterns: List[str]
    vulnerabilities: List[str]
    recommendations: List[str]
    estimated_crack_time: str
    is_common: bool
    similarity_to_common: float

class PasswordScanner:
    """فاحص كلمات المرور المتقدم"""

    def __init__(self):
        self.common_passwords = self._load_common_passwords()
        self.common_patterns = self._load_common_patterns()
        # تحسين الذاكرة
        self.common_passwords = set(list(self.common_passwords)[:500])  # تحديد العدد

    def _load_common_passwords(self) -> set:
        """تحميل قائمة كلمات المرور الشائعة"""
        # قائمة كلمات المرور الأكثر شيوعاً
        common = {
            "123456", "password", "123456789", "12345678", "12345",
            "1234567", "1234567890", "qwerty", "abc123", "111111",
            "123123", "admin", "letmein", "welcome", "monkey",
            "password123", "123qwe", "qwerty123", "iloveyou", "princess",
            "dragon", "sunshine", "master", "shadow", "football",
            "michael", "computer", "superman", "internet", "service",
            "canada", "hello", "ranger", "jordan", "tiger",
            "michelle", "daniel", "andrew", "andrea", "joshua"
        }

        # إضافة كلمات مرور عربية شائعة
        arabic_common = {
            "123456", "احبك", "محمد", "احمد", "علي", "فاطمة",
            "مرحبا", "كلمةمرور", "السعودية", "مصر", "الاردن",
            "فلسطين", "العراق", "سوريا", "لبنان", "الكويت"
        }

        return common.union(arabic_common)

    def _load_common_patterns(self) -> List[str]:
        """تحميل الأنماط الشائعة الضعيفة"""
        return [
            r'\d{4,}',  # أرقام متتالية
            r'(.)\1{2,}',  # تكرار نفس الحرف
            r'(abc|123|qwe|asd)',  # تسلسلات لوحة المفاتيح
            r'\d{1,2}[/-]\d{1,2}[/-]\d{2,4}',  # تواريخ
            r'(password|pass|pwd)',  # كلمات مرور واضحة
            r'^[a-z]+$',  # أحرف صغيرة فقط
            r'^[A-Z]+$',  # أحرف كبيرة فقط
            r'^\d+$',  # أرقام فقط
        ]

    @cache_result(ttl=600)  # كاش لمدة 10 دقائق
    @time_execution
    def analyze_password(self, password: str) -> PasswordAnalysis:
        """تحليل شامل لكلمة المرور"""

        # التحليلات الأساسية
        length = len(password)
        character_sets = self._analyze_character_sets(password)
        entropy = self._calculate_entropy(password, character_sets)
        patterns = self._detect_patterns(password)
        vulnerabilities = self._detect_vulnerabilities(password, patterns)

        # فحص الشيوع
        is_common = password.lower() in self.common_passwords
        similarity = self._calculate_similarity_to_common(password)

        # حساب النقاط والقوة
        score = self._calculate_score(password, character_sets, entropy, patterns, is_common)
        strength = self._determine_strength(score)

        # تقدير وقت الكسر
        crack_time = self._estimate_crack_time(entropy, length)

        # التوصيات
        recommendations = self._generate_recommendations(
            password, character_sets, patterns, vulnerabilities, length
        )

        return PasswordAnalysis(
            password=password,
            strength=strength,
            score=score,
            entropy=entropy,
            length=length,
            character_sets=character_sets,
            patterns=patterns,
            vulnerabilities=vulnerabilities,
            recommendations=recommendations,
            estimated_crack_time=crack_time,
            is_common=is_common,
            similarity_to_common=similarity
        )

    def _analyze_character_sets(self, password: str) -> Dict[str, bool]:
        """تحليل مجموعات الأحرف المستخدمة"""
        return {
            'lowercase': any(c.islower() for c in password),
            'uppercase': any(c.isupper() for c in password),
            'digits': any(c.isdigit() for c in password),
            'special': any(c in string.punctuation for c in password),
            'arabic': any('\u0600' <= c <= '\u06FF' for c in password),
            'spaces': ' ' in password
        }

    def _calculate_entropy(self, password: str, character_sets: Dict[str, bool]) -> float:
        """حساب الإنتروبيا (العشوائية)"""
        charset_size = 0

        if character_sets['lowercase']:
            charset_size += 26
        if character_sets['uppercase']:
            charset_size += 26
        if character_sets['digits']:
            charset_size += 10
        if character_sets['special']:
            charset_size += 32
        if character_sets['arabic']:
            charset_size += 28

        if charset_size == 0:
            return 0

        return len(password) * math.log2(charset_size)

    def _detect_patterns(self, password: str) -> List[str]:
        """اكتشاف الأنماط الضعيفة"""
        detected_patterns = []

        for pattern in self.common_patterns:
            if re.search(pattern, password.lower()):
                detected_patterns.append(pattern)

        # فحص أنماط إضافية
        if self._is_keyboard_pattern(password):
            detected_patterns.append("keyboard_pattern")

        if self._is_repeated_pattern(password):
            detected_patterns.append("repeated_pattern")

        return detected_patterns

    def _is_keyboard_pattern(self, password: str) -> bool:
        """فحص أنماط لوحة المفاتيح"""
        keyboard_patterns = [
            "qwerty", "asdf", "zxcv", "123456", "qwertyuiop",
            "asdfghjkl", "zxcvbnm", "1234567890"
        ]

        password_lower = password.lower()
        for pattern in keyboard_patterns:
            if pattern in password_lower or pattern[::-1] in password_lower:
                return True
        return False

    def _is_repeated_pattern(self, password: str) -> bool:
        """فحص الأنماط المتكررة"""
        if len(password) < 4:
            return False

        for i in range(1, len(password) // 2 + 1):
            pattern = password[:i]
            if password == pattern * (len(password) // i):
                return True
        return False

    def _detect_vulnerabilities(self, password: str, patterns: List[str]) -> List[str]:
        """اكتشاف نقاط الضعف"""
        vulnerabilities = []

        if len(password) < 8:
            vulnerabilities.append("كلمة المرور قصيرة جداً (أقل من 8 أحرف)")

        if password.lower() in self.common_passwords:
            vulnerabilities.append("كلمة مرور شائعة ومعروفة")

        if password.isdigit():
            vulnerabilities.append("تحتوي على أرقام فقط")

        if password.isalpha():
            vulnerabilities.append("تحتوي على أحرف فقط")

        if password.islower():
            vulnerabilities.append("تحتوي على أحرف صغيرة فقط")

        if password.isupper():
            vulnerabilities.append("تحتوي على أحرف كبيرة فقط")

        if any(pattern in patterns for pattern in ["keyboard_pattern", "repeated_pattern"]):
            vulnerabilities.append("تحتوي على أنماط متكررة أو لوحة مفاتيح")

        if re.search(r'\d{4,}', password):
            vulnerabilities.append("تحتوي على أرقام متتالية طويلة")

        return vulnerabilities

    def _calculate_similarity_to_common(self, password: str) -> float:
        """حساب التشابه مع كلمات المرور الشائعة"""
        max_similarity = 0
        password_lower = password.lower()

        for common_pwd in self.common_passwords:
            similarity = self._calculate_string_similarity(password_lower, common_pwd)
            max_similarity = max(max_similarity, similarity)

        return max_similarity

    def _calculate_string_similarity(self, str1: str, str2: str) -> float:
        """حساب التشابه بين نصين"""
        if not str1 or not str2:
            return 0

        # خوارزمية Levenshtein distance مبسطة
        len1, len2 = len(str1), len(str2)
        if len1 > len2:
            str1, str2 = str2, str1
            len1, len2 = len2, len1

        current_row = range(len1 + 1)
        for i in range(1, len2 + 1):
            previous_row, current_row = current_row, [i] + [0] * len1
            for j in range(1, len1 + 1):
                add, delete, change = previous_row[j] + 1, current_row[j - 1] + 1, previous_row[j - 1]
                if str1[j - 1] != str2[i - 1]:
                    change += 1
                current_row[j] = min(add, delete, change)

        distance = current_row[len1]
        return 1 - (distance / max(len1, len2))

    def _calculate_score(self, password: str, character_sets: Dict[str, bool],
                        entropy: float, patterns: List[str], is_common: bool) -> int:
        """حساب نقاط قوة كلمة المرور"""
        score = 0

        # نقاط الطول
        length = len(password)
        if length >= 12:
            score += 25
        elif length >= 8:
            score += 15
        elif length >= 6:
            score += 10
        else:
            score += 5

        # نقاط تنوع الأحرف
        char_variety = sum(character_sets.values())
        score += char_variety * 5

        # نقاط الإنتروبيا
        if entropy >= 60:
            score += 25
        elif entropy >= 40:
            score += 20
        elif entropy >= 25:
            score += 15
        else:
            score += 10

        # خصم نقاط للأنماط الضعيفة
        score -= len(patterns) * 10

        # خصم نقاط لكلمات المرور الشائعة
        if is_common:
            score -= 30

        # التأكد من أن النقاط بين 0 و 100
        return max(0, min(100, score))

    def _determine_strength(self, score: int) -> PasswordStrength:
        """تحديد مستوى قوة كلمة المرور"""
        if score >= 80:
            return PasswordStrength.VERY_STRONG
        elif score >= 60:
            return PasswordStrength.STRONG
        elif score >= 40:
            return PasswordStrength.MODERATE
        elif score >= 20:
            return PasswordStrength.WEAK
        else:
            return PasswordStrength.VERY_WEAK

    def _estimate_crack_time(self, entropy: float, length: int) -> str:
        """تقدير وقت كسر كلمة المرور"""
        # افتراض 1 مليار محاولة في الثانية
        attempts_per_second = 1_000_000_000

        # عدد المحاولات المطلوبة (في المتوسط نصف المساحة)
        total_combinations = 2 ** entropy
        average_attempts = total_combinations / 2

        seconds = average_attempts / attempts_per_second

        if seconds < 1:
            return "أقل من ثانية"
        elif seconds < 60:
            return f"{int(seconds)} ثانية"
        elif seconds < 3600:
            return f"{int(seconds / 60)} دقيقة"
        elif seconds < 86400:
            return f"{int(seconds / 3600)} ساعة"
        elif seconds < 31536000:
            return f"{int(seconds / 86400)} يوم"
        else:
            years = int(seconds / 31536000)
            if years > 1000000:
                return "أكثر من مليون سنة"
            return f"{years} سنة"

    def _generate_recommendations(self, password: str, character_sets: Dict[str, bool],
                                patterns: List[str], vulnerabilities: List[str],
                                length: int) -> List[str]:
        """توليد توصيات لتحسين كلمة المرور"""
        recommendations = []

        if length < 8:
            recommendations.append("استخدم كلمة مرور أطول (8 أحرف على الأقل)")
        elif length < 12:
            recommendations.append("استخدم كلمة مرور أطول (12 حرف أو أكثر للأمان الأفضل)")

        if not character_sets['lowercase']:
            recommendations.append("أضف أحرف صغيرة (a-z)")

        if not character_sets['uppercase']:
            recommendations.append("أضف أحرف كبيرة (A-Z)")

        if not character_sets['digits']:
            recommendations.append("أضف أرقام (0-9)")

        if not character_sets['special']:
            recommendations.append("أضف رموز خاصة (!@#$%^&*)")

        if "keyboard_pattern" in patterns:
            recommendations.append("تجنب أنماط لوحة المفاتيح (qwerty, 123456)")

        if "repeated_pattern" in patterns:
            recommendations.append("تجنب تكرار الأحرف أو الأنماط")

        if password.lower() in self.common_passwords:
            recommendations.append("تجنب كلمات المرور الشائعة والمعروفة")

        if re.search(r'\d{4,}', password):
            recommendations.append("تجنب الأرقام المتتالية الطويلة")

        if not recommendations:
            recommendations.append("كلمة المرور قوية! حافظ على سريتها")

        return recommendations
