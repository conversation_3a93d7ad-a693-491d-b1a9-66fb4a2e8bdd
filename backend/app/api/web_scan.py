"""
API endpoints لفحص ثغرات تطبيقات الويب
"""

from fastapi import APIRouter, HTTPException, BackgroundTasks
from pydantic import BaseModel, Field, HttpUrl
from typing import List, Dict, Optional
from datetime import datetime
import asyncio

from app.scanners.web_vulnerability_scanner import (
    WebVulnerabilityScanner,
    Vulnerability,
    ScanResult,
    VulnerabilityType,
    SeverityLevel
)

router = APIRouter()

# إنشاء مثيل من فاحص ثغرات الويب
web_scanner = WebVulnerabilityScanner()

class WebScanRequest(BaseModel):
    """طلب فحص موقع ويب"""
    target_url: str = Field(..., description="رابط الموقع المراد فحصه")
    max_depth: int = Field(default=2, ge=1, le=5, description="عمق الاستكشاف (1-5)")
    scan_types: List[str] = Field(
        default=["sql_injection", "xss", "directory_traversal", "command_injection", "csrf"],
        description="أنواع الفحص المطلوبة"
    )
    aggressive_mode: bool = Field(default=False, description="الوضع العدواني (المزيد من الاختبارات)")

class VulnerabilityResponse(BaseModel):
    """استجابة الثغرة المكتشفة"""
    type: str = Field(..., description="نوع الثغرة")
    severity: str = Field(..., description="مستوى الخطورة")
    url: str = Field(..., description="الرابط المتأثر")
    parameter: Optional[str] = Field(None, description="المعامل المتأثر")
    payload: str = Field(..., description="الحمولة المستخدمة")
    evidence: str = Field(..., description="دليل الثغرة")
    description: str = Field(..., description="وصف الثغرة")
    impact: str = Field(..., description="تأثير الثغرة")
    remediation: str = Field(..., description="طريقة الإصلاح")
    confidence: float = Field(..., ge=0, le=1, description="مستوى الثقة")

class WebScanResponse(BaseModel):
    """استجابة فحص الموقع"""
    target_url: str = Field(..., description="الموقع المفحوص")
    scan_status: str = Field(..., description="حالة الفحص")
    vulnerabilities: List[VulnerabilityResponse] = Field(..., description="الثغرات المكتشفة")
    scan_duration: float = Field(..., description="مدة الفحص بالثواني")
    pages_scanned: int = Field(..., description="عدد الصفحات المفحوصة")
    forms_found: int = Field(..., description="عدد النماذج الموجودة")
    parameters_tested: int = Field(..., description="عدد المعاملات المختبرة")
    summary: Dict[str, int] = Field(..., description="ملخص النتائج")
    scan_timestamp: datetime = Field(default_factory=datetime.now, description="وقت الفحص")
    recommendations: List[str] = Field(..., description="توصيات الأمان")

class QuickScanRequest(BaseModel):
    """طلب فحص سريع"""
    target_url: str = Field(..., description="رابط الموقع")
    vulnerability_type: str = Field(..., description="نوع الثغرة المحدد للفحص")

@router.post("/scan", response_model=WebScanResponse)
async def scan_website(request: WebScanRequest):
    """
    فحص شامل لموقع ويب للثغرات الأمنية

    يقوم بفحص الموقع للثغرات التالية:
    - SQL Injection
    - Cross-Site Scripting (XSS)
    - Directory Traversal
    - Command Injection
    - CSRF
    - Open Redirect
    - Information Disclosure
    """
    try:
        # التحقق من صحة URL
        if not request.target_url.startswith(('http://', 'https://')):
            request.target_url = 'http://' + request.target_url

        # تشغيل الفحص
        scan_result = web_scanner.scan_website(request.target_url, request.max_depth)

        # تحويل النتائج إلى تنسيق الاستجابة
        vulnerabilities = []
        for vuln in scan_result.vulnerabilities:
            vuln_response = VulnerabilityResponse(
                type=vuln.type.value,
                severity=vuln.severity.value,
                url=vuln.url,
                parameter=vuln.parameter,
                payload=vuln.payload,
                evidence=vuln.evidence,
                description=vuln.description,
                impact=vuln.impact,
                remediation=vuln.remediation,
                confidence=vuln.confidence
            )
            vulnerabilities.append(vuln_response)

        # توليد التوصيات
        recommendations = _generate_recommendations(scan_result)

        return WebScanResponse(
            target_url=scan_result.target_url,
            scan_status="مكتمل",
            vulnerabilities=vulnerabilities,
            scan_duration=scan_result.scan_duration,
            pages_scanned=scan_result.pages_scanned,
            forms_found=scan_result.forms_found,
            parameters_tested=scan_result.parameters_tested,
            summary=scan_result.summary,
            recommendations=recommendations
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"خطأ في فحص الموقع: {str(e)}")

@router.post("/quick-scan", response_model=WebScanResponse)
async def quick_scan(request: QuickScanRequest):
    """
    فحص سريع لنوع ثغرة محدد

    يقوم بفحص سريع للموقع لنوع واحد من الثغرات
    """
    try:
        # فحص محدود لصفحة واحدة
        scan_result = web_scanner.scan_website(request.target_url, max_depth=1)

        # فلترة النتائج حسب نوع الثغرة المطلوب
        filtered_vulns = []
        for vuln in scan_result.vulnerabilities:
            if request.vulnerability_type.lower() in vuln.type.value.lower():
                vuln_response = VulnerabilityResponse(
                    type=vuln.type.value,
                    severity=vuln.severity.value,
                    url=vuln.url,
                    parameter=vuln.parameter,
                    payload=vuln.payload,
                    evidence=vuln.evidence,
                    description=vuln.description,
                    impact=vuln.impact,
                    remediation=vuln.remediation,
                    confidence=vuln.confidence
                )
                filtered_vulns.append(vuln_response)

        # إعادة حساب الملخص
        summary = {
            "total": len(filtered_vulns),
            "critical": sum(1 for v in filtered_vulns if v.severity == "حرج"),
            "high": sum(1 for v in filtered_vulns if v.severity == "عالي"),
            "medium": sum(1 for v in filtered_vulns if v.severity == "متوسط"),
            "low": sum(1 for v in filtered_vulns if v.severity == "منخفض")
        }

        return WebScanResponse(
            target_url=scan_result.target_url,
            scan_status="مكتمل (فحص سريع)",
            vulnerabilities=filtered_vulns,
            scan_duration=scan_result.scan_duration,
            pages_scanned=scan_result.pages_scanned,
            forms_found=scan_result.forms_found,
            parameters_tested=scan_result.parameters_tested,
            summary=summary,
            recommendations=_generate_specific_recommendations(request.vulnerability_type, filtered_vulns)
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"خطأ في الفحص السريع: {str(e)}")

@router.get("/vulnerability-types")
async def get_vulnerability_types():
    """
    الحصول على أنواع الثغرات المدعومة
    """
    return {
        "vulnerability_types": [
            {
                "id": "sql_injection",
                "name": "SQL Injection",
                "description": "ثغرات حقن قواعد البيانات",
                "severity": "عالي",
                "common": True
            },
            {
                "id": "xss",
                "name": "Cross-Site Scripting",
                "description": "ثغرات تنفيذ الأكواد في المتصفح",
                "severity": "متوسط",
                "common": True
            },
            {
                "id": "directory_traversal",
                "name": "Directory Traversal",
                "description": "ثغرات الوصول للملفات",
                "severity": "عالي",
                "common": True
            },
            {
                "id": "command_injection",
                "name": "Command Injection",
                "description": "ثغرات تنفيذ أوامر النظام",
                "severity": "حرج",
                "common": False
            },
            {
                "id": "csrf",
                "name": "Cross-Site Request Forgery",
                "description": "ثغرات الطلبات المزورة",
                "severity": "متوسط",
                "common": True
            },
            {
                "id": "open_redirect",
                "name": "Open Redirect",
                "description": "ثغرات إعادة التوجيه المفتوحة",
                "severity": "منخفض",
                "common": False
            }
        ]
    }

@router.get("/scan-templates")
async def get_scan_templates():
    """
    الحصول على قوالب الفحص المحددة مسبقاً
    """
    return {
        "templates": [
            {
                "name": "فحص أساسي",
                "description": "فحص الثغرات الأكثر شيوعاً",
                "scan_types": ["sql_injection", "xss", "csrf"],
                "max_depth": 2,
                "aggressive_mode": False
            },
            {
                "name": "فحص شامل",
                "description": "فحص جميع أنواع الثغرات",
                "scan_types": ["sql_injection", "xss", "directory_traversal", "command_injection", "csrf", "open_redirect"],
                "max_depth": 3,
                "aggressive_mode": True
            },
            {
                "name": "فحص سريع",
                "description": "فحص سريع للثغرات الحرجة",
                "scan_types": ["sql_injection", "command_injection"],
                "max_depth": 1,
                "aggressive_mode": False
            }
        ]
    }

def _generate_recommendations(scan_result: ScanResult) -> List[str]:
    """توليد توصيات الأمان"""
    recommendations = []

    if scan_result.summary.get("critical", 0) > 0:
        recommendations.append("🚨 يوجد ثغرات حرجة تتطلب إصلاح فوري")

    if scan_result.summary.get("high", 0) > 0:
        recommendations.append("⚠️ يوجد ثغرات عالية الخطورة تحتاج إصلاح سريع")

    if scan_result.summary.get("total", 0) == 0:
        recommendations.append("✅ لم يتم اكتشاف ثغرات واضحة في الفحص الحالي")
        recommendations.append("🔍 يُنصح بإجراء فحص أعمق أو استخدام أدوات إضافية")

    # توصيات عامة
    recommendations.extend([
        "🔐 استخدم HTTPS لجميع الاتصالات",
        "🛡️ طبق مبدأ التحقق من صحة جميع المدخلات",
        "🔄 قم بتحديث جميع المكتبات والإطارات بانتظام",
        "📝 راجع سجلات الأمان بشكل دوري"
    ])

    return recommendations

def _generate_specific_recommendations(vuln_type: str, vulnerabilities: List[VulnerabilityResponse]) -> List[str]:
    """توليد توصيات محددة لنوع ثغرة"""
    recommendations = []

    if not vulnerabilities:
        recommendations.append(f"✅ لم يتم اكتشاف ثغرات من نوع {vuln_type}")
        return recommendations

    if "sql" in vuln_type.lower():
        recommendations.extend([
            "استخدم Prepared Statements",
            "طبق مبدأ Least Privilege لقواعد البيانات",
            "فعل WAF للحماية من SQL Injection"
        ])
    elif "xss" in vuln_type.lower():
        recommendations.extend([
            "قم بترميز جميع المخرجات",
            "استخدم Content Security Policy (CSP)",
            "طبق Input Validation صارم"
        ])
    elif "command" in vuln_type.lower():
        recommendations.extend([
            "تجنب تنفيذ أوامر النظام مع مدخلات المستخدم",
            "استخدم مكتبات آمنة بدلاً من system calls",
            "طبق Sandboxing للعمليات الحساسة"
        ])

    return recommendations
