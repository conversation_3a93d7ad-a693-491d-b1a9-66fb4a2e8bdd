"""
API endpoints لفحص إعدادات الخوادم
"""

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, Field
from typing import List, Dict, Optional
from datetime import datetime

from app.scanners.server_config_scanner import (
    ServerConfigScanner,
    ServerScanResult,
    ServerIssue,
    SSLInfo,
    SecurityHeaders,
    ServerIssueType,
    ServerSeverity
)

router = APIRouter()

# إنشاء مثيل من فاحص إعدادات الخوادم
server_scanner = ServerConfigScanner()

class ServerScanRequest(BaseModel):
    """طلب فحص إعدادات الخادم"""
    target: str = Field(..., description="الهدف (domain أو IP)")
    port_scan: bool = Field(default=True, description="فحص البورتات")
    ssl_scan: bool = Field(default=True, description="فحص SSL/TLS")
    header_scan: bool = Field(default=True, description="فحص رؤوس الأمان")

class ServerIssueResponse(BaseModel):
    """استجابة مشكلة الخادم"""
    type: str = Field(..., description="نوع المشكلة")
    severity: str = Field(..., description="مستوى الخطورة")
    title: str = Field(..., description="عنوان المشكلة")
    description: str = Field(..., description="وصف المشكلة")
    evidence: str = Field(..., description="دليل المشكلة")
    impact: str = Field(..., description="تأثير المشكلة")
    remediation: str = Field(..., description="طريقة الإصلاح")
    port: Optional[int] = Field(None, description="البورت المتأثر")
    service: Optional[str] = Field(None, description="الخدمة المتأثرة")

class SSLInfoResponse(BaseModel):
    """استجابة معلومات SSL"""
    version: str = Field(..., description="إصدار SSL/TLS")
    cipher: str = Field(..., description="خوارزمية التشفير")
    certificate_valid: bool = Field(..., description="صحة الشهادة")
    certificate_expired: bool = Field(..., description="انتهاء صلاحية الشهادة")
    certificate_self_signed: bool = Field(..., description="شهادة موقعة ذاتياً")
    certificate_issuer: str = Field(..., description="مُصدر الشهادة")
    certificate_subject: str = Field(..., description="موضوع الشهادة")
    certificate_expiry: Optional[datetime] = Field(None, description="تاريخ انتهاء الصلاحية")
    supported_protocols: List[str] = Field(..., description="البروتوكولات المدعومة")
    weak_ciphers: List[str] = Field(..., description="التشفير الضعيف")

class SecurityHeadersResponse(BaseModel):
    """استجابة رؤوس الأمان"""
    strict_transport_security: Optional[str] = Field(None, description="HSTS")
    content_security_policy: Optional[str] = Field(None, description="CSP")
    x_frame_options: Optional[str] = Field(None, description="X-Frame-Options")
    x_content_type_options: Optional[str] = Field(None, description="X-Content-Type-Options")
    x_xss_protection: Optional[str] = Field(None, description="X-XSS-Protection")
    referrer_policy: Optional[str] = Field(None, description="Referrer-Policy")
    permissions_policy: Optional[str] = Field(None, description="Permissions-Policy")

class ServerScanResponse(BaseModel):
    """استجابة فحص الخادم"""
    target: str = Field(..., description="الهدف المفحوص")
    ip_address: str = Field(..., description="عنوان IP")
    open_ports: List[int] = Field(..., description="البورتات المفتوحة")
    services: Dict[int, str] = Field(..., description="الخدمات المكتشفة")
    ssl_info: Optional[SSLInfoResponse] = Field(None, description="معلومات SSL")
    security_headers: SecurityHeadersResponse = Field(..., description="رؤوس الأمان")
    server_info: Dict[str, str] = Field(..., description="معلومات الخادم")
    issues: List[ServerIssueResponse] = Field(..., description="المشاكل المكتشفة")
    scan_duration: float = Field(..., description="مدة الفحص")
    summary: Dict[str, int] = Field(..., description="ملخص النتائج")
    scan_timestamp: datetime = Field(default_factory=datetime.now, description="وقت الفحص")
    recommendations: List[str] = Field(..., description="توصيات الأمان")

class SSLOnlyRequest(BaseModel):
    """طلب فحص SSL فقط"""
    target: str = Field(..., description="الهدف")
    port: int = Field(default=443, description="البورت")

@router.post("/scan", response_model=ServerScanResponse)
async def scan_server(request: ServerScanRequest):
    """
    فحص شامل لإعدادات الخادم

    يقوم بفحص:
    - البورتات المفتوحة والخدمات
    - إعدادات SSL/TLS والشهادات
    - رؤوس الأمان HTTP
    - معلومات الخادم المكشوفة
    """
    try:
        # تشغيل الفحص
        scan_result = server_scanner.scan_server(
            target=request.target,
            port_scan=request.port_scan,
            ssl_scan=request.ssl_scan,
            header_scan=request.header_scan
        )

        # تحويل النتائج إلى تنسيق الاستجابة
        issues = []
        for issue in scan_result.issues:
            issue_response = ServerIssueResponse(
                type=issue.type.value,
                severity=issue.severity.value,
                title=issue.title,
                description=issue.description,
                evidence=issue.evidence,
                impact=issue.impact,
                remediation=issue.remediation,
                port=issue.port,
                service=issue.service
            )
            issues.append(issue_response)

        # تحويل معلومات SSL
        ssl_info_response = None
        if scan_result.ssl_info:
            ssl_info_response = SSLInfoResponse(
                version=scan_result.ssl_info.version,
                cipher=scan_result.ssl_info.cipher,
                certificate_valid=scan_result.ssl_info.certificate_valid,
                certificate_expired=scan_result.ssl_info.certificate_expired,
                certificate_self_signed=scan_result.ssl_info.certificate_self_signed,
                certificate_issuer=scan_result.ssl_info.certificate_issuer,
                certificate_subject=scan_result.ssl_info.certificate_subject,
                certificate_expiry=scan_result.ssl_info.certificate_expiry,
                supported_protocols=scan_result.ssl_info.supported_protocols,
                weak_ciphers=scan_result.ssl_info.weak_ciphers
            )

        # تحويل رؤوس الأمان
        security_headers_response = SecurityHeadersResponse(
            strict_transport_security=scan_result.security_headers.strict_transport_security,
            content_security_policy=scan_result.security_headers.content_security_policy,
            x_frame_options=scan_result.security_headers.x_frame_options,
            x_content_type_options=scan_result.security_headers.x_content_type_options,
            x_xss_protection=scan_result.security_headers.x_xss_protection,
            referrer_policy=scan_result.security_headers.referrer_policy,
            permissions_policy=scan_result.security_headers.permissions_policy
        )

        # توليد التوصيات
        recommendations = _generate_recommendations(scan_result)

        return ServerScanResponse(
            target=scan_result.target,
            ip_address=scan_result.ip_address,
            open_ports=scan_result.open_ports,
            services=scan_result.services,
            ssl_info=ssl_info_response,
            security_headers=security_headers_response,
            server_info=scan_result.server_info,
            issues=issues,
            scan_duration=scan_result.scan_duration,
            summary=scan_result.summary,
            recommendations=recommendations
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"خطأ في فحص الخادم: {str(e)}")

@router.post("/ssl-scan", response_model=SSLInfoResponse)
async def scan_ssl_only(request: SSLOnlyRequest):
    """
    فحص SSL/TLS فقط

    فحص مركز على إعدادات SSL/TLS والشهادات
    """
    try:
        # فحص SSL فقط
        ssl_info = server_scanner._scan_ssl(request.target, request.port)

        return SSLInfoResponse(
            version=ssl_info.version,
            cipher=ssl_info.cipher,
            certificate_valid=ssl_info.certificate_valid,
            certificate_expired=ssl_info.certificate_expired,
            certificate_self_signed=ssl_info.certificate_self_signed,
            certificate_issuer=ssl_info.certificate_issuer,
            certificate_subject=ssl_info.certificate_subject,
            certificate_expiry=ssl_info.certificate_expiry,
            supported_protocols=ssl_info.supported_protocols,
            weak_ciphers=ssl_info.weak_ciphers
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"خطأ في فحص SSL: {str(e)}")

@router.get("/security-headers/{target}")
async def scan_headers_only(target: str):
    """
    فحص رؤوس الأمان فقط

    فحص مركز على رؤوس الأمان HTTP
    """
    try:
        if not target.startswith(('http://', 'https://')):
            target = 'https://' + target

        security_headers, server_info = server_scanner._scan_headers(target)

        return {
            "target": target,
            "security_headers": {
                "strict_transport_security": security_headers.strict_transport_security,
                "content_security_policy": security_headers.content_security_policy,
                "x_frame_options": security_headers.x_frame_options,
                "x_content_type_options": security_headers.x_content_type_options,
                "x_xss_protection": security_headers.x_xss_protection,
                "referrer_policy": security_headers.referrer_policy,
                "permissions_policy": security_headers.permissions_policy
            },
            "server_info": server_info,
            "scan_timestamp": datetime.now()
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"خطأ في فحص الرؤوس: {str(e)}")

@router.get("/common-ports")
async def get_common_ports():
    """
    الحصول على البورتات الشائعة المفحوصة
    """
    return {
        "common_ports": [
            {"port": 21, "service": "FTP", "secure": False},
            {"port": 22, "service": "SSH", "secure": True},
            {"port": 23, "service": "Telnet", "secure": False},
            {"port": 25, "service": "SMTP", "secure": False},
            {"port": 53, "service": "DNS", "secure": True},
            {"port": 80, "service": "HTTP", "secure": False},
            {"port": 110, "service": "POP3", "secure": False},
            {"port": 143, "service": "IMAP", "secure": False},
            {"port": 443, "service": "HTTPS", "secure": True},
            {"port": 993, "service": "IMAPS", "secure": True},
            {"port": 995, "service": "POP3S", "secure": True},
            {"port": 8080, "service": "HTTP-Alt", "secure": False},
            {"port": 8443, "service": "HTTPS-Alt", "secure": True}
        ]
    }

@router.get("/security-headers-info")
async def get_security_headers_info():
    """
    معلومات حول رؤوس الأمان المهمة
    """
    return {
        "headers": [
            {
                "name": "Strict-Transport-Security",
                "purpose": "يفرض استخدام HTTPS",
                "example": "max-age=31536000; includeSubDomains",
                "importance": "عالي"
            },
            {
                "name": "Content-Security-Policy",
                "purpose": "يمنع هجمات XSS وحقن المحتوى",
                "example": "default-src 'self'",
                "importance": "عالي"
            },
            {
                "name": "X-Frame-Options",
                "purpose": "يمنع هجمات Clickjacking",
                "example": "DENY",
                "importance": "متوسط"
            },
            {
                "name": "X-Content-Type-Options",
                "purpose": "يمنع MIME type sniffing",
                "example": "nosniff",
                "importance": "متوسط"
            },
            {
                "name": "X-XSS-Protection",
                "purpose": "يفعل حماية XSS في المتصفح",
                "example": "1; mode=block",
                "importance": "متوسط"
            }
        ]
    }

def _generate_recommendations(scan_result: ServerScanResult) -> List[str]:
    """توليد توصيات الأمان للخادم"""
    recommendations = []

    # توصيات حسب نوع المشاكل
    issue_types = [issue.type for issue in scan_result.issues]

    if ServerIssueType.SSL_EXPIRED_CERT in issue_types:
        recommendations.append("🚨 جدد شهادة SSL فوراً لتجنب تحذيرات المتصفح")

    if ServerIssueType.WEAK_SSL_VERSION in issue_types:
        recommendations.append("🔒 عطل البروتوكولات القديمة واستخدم TLS 1.2+ فقط")

    if ServerIssueType.MISSING_SECURITY_HEADER in issue_types:
        recommendations.append("🛡️ أضف رؤوس الأمان المفقودة لتحسين الحماية")

    if ServerIssueType.INSECURE_SERVICE in issue_types:
        recommendations.append("⚠️ أغلق الخدمات غير الآمنة أو استخدم البدائل المشفرة")

    if ServerIssueType.INFORMATION_DISCLOSURE in issue_types:
        recommendations.append("🔍 أخف معلومات الخادم لتقليل سطح الهجوم")

    # توصيات عامة
    if scan_result.summary.get("total", 0) == 0:
        recommendations.append("✅ إعدادات الخادم تبدو آمنة نسبياً")

    recommendations.extend([
        "🔄 قم بفحص دوري لإعدادات الأمان",
        "📊 راقب سجلات الخادم للأنشطة المشبوهة",
        "🔐 استخدم كلمات مرور قوية لجميع الخدمات",
        "⬆️ حدث جميع البرامج والخدمات بانتظام"
    ])

    return recommendations
