"""
API endpoints لفحص كلمات المرور
"""

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, Field
from typing import List, Dict, Optional
from datetime import datetime

from app.scanners.password_scanner import PasswordScanner, PasswordAnalysis, PasswordStrength

router = APIRouter()

# إنشاء مثيل من فاحص كلمات المرور
password_scanner = PasswordScanner()

class PasswordRequest(BaseModel):
    """طلب فحص كلمة المرور"""
    password: str = Field(..., min_length=1, max_length=128, description="كلمة المرور المراد فحصها")
    include_password_in_response: bool = Field(default=False, description="تضمين كلمة المرور في الاستجابة")

class PasswordResponse(BaseModel):
    """استجابة فحص كلمة المرور"""
    password: Optional[str] = Field(None, description="كلمة المرور (اختياري)")
    strength: str = Field(..., description="مستوى قوة كلمة المرور")
    score: int = Field(..., ge=0, le=100, description="نقاط القوة من 0 إلى 100")
    entropy: float = Field(..., description="الإنتروبيا (العشوائية)")
    length: int = Field(..., description="طول كلمة المرور")
    character_sets: Dict[str, bool] = Field(..., description="مجموعات الأحرف المستخدمة")
    patterns: List[str] = Field(..., description="الأنماط المكتشفة")
    vulnerabilities: List[str] = Field(..., description="نقاط الضعف")
    recommendations: List[str] = Field(..., description="توصيات التحسين")
    estimated_crack_time: str = Field(..., description="تقدير وقت الكسر")
    is_common: bool = Field(..., description="هل كلمة المرور شائعة")
    similarity_to_common: float = Field(..., ge=0, le=1, description="التشابه مع كلمات المرور الشائعة")
    scan_timestamp: datetime = Field(default_factory=datetime.now, description="وقت الفحص")

class BatchPasswordRequest(BaseModel):
    """طلب فحص متعدد لكلمات المرور"""
    passwords: List[str] = Field(..., min_items=1, max_items=10, description="قائمة كلمات المرور")
    include_passwords_in_response: bool = Field(default=False, description="تضمين كلمات المرور في الاستجابة")

class BatchPasswordResponse(BaseModel):
    """استجابة فحص متعدد لكلمات المرور"""
    results: List[PasswordResponse] = Field(..., description="نتائج الفحص")
    summary: Dict[str, int] = Field(..., description="ملخص النتائج")
    scan_timestamp: datetime = Field(default_factory=datetime.now, description="وقت الفحص")

@router.post("/analyze", response_model=PasswordResponse)
async def analyze_password(request: PasswordRequest):
    """
    فحص وتحليل كلمة مرور واحدة

    يقوم بتحليل شامل لكلمة المرور ويعطي:
    - مستوى القوة والنقاط
    - الإنتروبيا وتقدير وقت الكسر
    - نقاط الضعف والتوصيات
    """
    try:
        # تحليل كلمة المرور
        analysis = password_scanner.analyze_password(request.password)

        # إنشاء الاستجابة
        response = PasswordResponse(
            password=request.password if request.include_password_in_response else None,
            strength=analysis.strength.value,
            score=analysis.score,
            entropy=analysis.entropy,
            length=analysis.length,
            character_sets=analysis.character_sets,
            patterns=analysis.patterns,
            vulnerabilities=analysis.vulnerabilities,
            recommendations=analysis.recommendations,
            estimated_crack_time=analysis.estimated_crack_time,
            is_common=analysis.is_common,
            similarity_to_common=analysis.similarity_to_common
        )

        return response

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"خطأ في تحليل كلمة المرور: {str(e)}")

@router.post("/analyze-batch", response_model=BatchPasswordResponse)
async def analyze_passwords_batch(request: BatchPasswordRequest):
    """
    فحص وتحليل عدة كلمات مرور دفعة واحدة

    يقوم بتحليل قائمة من كلمات المرور ويعطي:
    - نتائج مفصلة لكل كلمة مرور
    - ملخص إحصائي للنتائج
    """
    try:
        results = []
        strength_counts = {
            "ضعيف جداً": 0,
            "ضعيف": 0,
            "متوسط": 0,
            "قوي": 0,
            "قوي جداً": 0
        }

        # تحليل كل كلمة مرور
        for password in request.passwords:
            analysis = password_scanner.analyze_password(password)

            # إحصاء مستويات القوة
            strength_counts[analysis.strength.value] += 1

            # إضافة النتيجة
            result = PasswordResponse(
                password=password if request.include_passwords_in_response else None,
                strength=analysis.strength.value,
                score=analysis.score,
                entropy=analysis.entropy,
                length=analysis.length,
                character_sets=analysis.character_sets,
                patterns=analysis.patterns,
                vulnerabilities=analysis.vulnerabilities,
                recommendations=analysis.recommendations,
                estimated_crack_time=analysis.estimated_crack_time,
                is_common=analysis.is_common,
                similarity_to_common=analysis.similarity_to_common
            )
            results.append(result)

        # إنشاء الملخص
        summary = {
            "total_passwords": len(request.passwords),
            "very_weak": strength_counts["ضعيف جداً"],
            "weak": strength_counts["ضعيف"],
            "moderate": strength_counts["متوسط"],
            "strong": strength_counts["قوي"],
            "very_strong": strength_counts["قوي جداً"],
            "common_passwords": sum(1 for r in results if r.is_common),
            "average_score": sum(r.score for r in results) // len(results) if results else 0
        }

        return BatchPasswordResponse(
            results=results,
            summary=summary
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"خطأ في تحليل كلمات المرور: {str(e)}")

@router.get("/strength-levels")
async def get_strength_levels():
    """
    الحصول على مستويات قوة كلمات المرور المتاحة
    """
    return {
        "strength_levels": [
            {"value": "ضعيف جداً", "min_score": 0, "max_score": 19, "color": "#ff4444"},
            {"value": "ضعيف", "min_score": 20, "max_score": 39, "color": "#ff8800"},
            {"value": "متوسط", "min_score": 40, "max_score": 59, "color": "#ffaa00"},
            {"value": "قوي", "min_score": 60, "max_score": 79, "color": "#88cc00"},
            {"value": "قوي جداً", "min_score": 80, "max_score": 100, "color": "#00cc44"}
        ],
        "description": "مستويات قوة كلمات المرور مع النقاط والألوان المقترحة"
    }

@router.get("/common-patterns")
async def get_common_patterns():
    """
    الحصول على الأنماط الشائعة الضعيفة
    """
    return {
        "patterns": [
            {"name": "keyboard_pattern", "description": "أنماط لوحة المفاتيح مثل qwerty"},
            {"name": "repeated_pattern", "description": "تكرار الأحرف أو الأنماط"},
            {"name": "sequential_numbers", "description": "أرقام متتالية طويلة"},
            {"name": "common_words", "description": "كلمات شائعة ومعروفة"},
            {"name": "dates", "description": "تواريخ بصيغ مختلفة"},
            {"name": "single_case", "description": "أحرف من نوع واحد فقط"}
        ],
        "recommendations": [
            "استخدم مزيج من الأحرف الكبيرة والصغيرة",
            "أضف أرقام ورموز خاصة",
            "تجنب الكلمات الشائعة والتواريخ",
            "استخدم كلمات مرور طويلة (12+ حرف)",
            "تجنب أنماط لوحة المفاتيح"
        ]
    }
