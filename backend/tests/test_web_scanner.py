"""
اختبارات وحدة فحص ثغرات الويب
"""

import sys
import os

# إضافة مسار التطبيق للاستيراد
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'app'))

from scanners.web_vulnerability_scanner import (
    WebVulnerabilityScanner,
    VulnerabilityType,
    SeverityLevel
)

class TestWebVulnerabilityScanner:
    """اختبارات فاحص ثغرات الويب"""

    def setup_method(self):
        """إعداد الاختبارات"""
        self.scanner = WebVulnerabilityScanner()

    def test_sql_payloads_loading(self):
        """اختبار تحميل payloads SQL Injection"""
        assert len(self.scanner.sql_payloads) > 0
        assert "' OR '1'='1" in self.scanner.sql_payloads
        assert "' UNION SELECT NULL--" in self.scanner.sql_payloads
        print("✅ تم تحميل SQL payloads بنجاح")

    def test_xss_payloads_loading(self):
        """اختبار تحميل payloads XSS"""
        assert len(self.scanner.xss_payloads) > 0
        assert "<script>alert('XSS')</script>" in self.scanner.xss_payloads
        assert "<img src=x onerror=alert('XSS')>" in self.scanner.xss_payloads
        print("✅ تم تحميل XSS payloads بنجاح")

    def test_traversal_payloads_loading(self):
        """اختبار تحميل payloads Directory Traversal"""
        assert len(self.scanner.traversal_payloads) > 0
        assert "../../../etc/passwd" in self.scanner.traversal_payloads
        assert "..\\..\\..\\windows\\system32\\drivers\\etc\\hosts" in self.scanner.traversal_payloads
        print("✅ تم تحميل Directory Traversal payloads بنجاح")

    def test_command_payloads_loading(self):
        """اختبار تحميل payloads Command Injection"""
        assert len(self.scanner.command_payloads) > 0
        assert "; ls -la" in self.scanner.command_payloads
        assert "| whoami" in self.scanner.command_payloads
        print("✅ تم تحميل Command Injection payloads بنجاح")

    def test_sql_injection_detection(self):
        """اختبار كشف SQL Injection"""
        # محاكاة استجابة تحتوي على خطأ SQL
        sql_error_response = "mysql_fetch_array(): supplied argument is not a valid MySQL result"

        result = self.scanner._detect_sql_injection(sql_error_response, "' OR 1=1--")
        assert result == True

        # استجابة عادية
        normal_response = "Welcome to our website"
        result = self.scanner._detect_sql_injection(normal_response, "' OR 1=1--")
        assert result == False

        print("✅ كشف SQL Injection يعمل بشكل صحيح")

    def test_xss_detection(self):
        """اختبار كشف XSS"""
        # محاكاة استجابة تحتوي على XSS payload خام (ثغرة مؤكدة)
        xss_payload = "<script>alert('XSS')</script>"
        xss_response = f"Hello {xss_payload} user"

        result = self.scanner._detect_xss(xss_response, xss_payload)
        assert result == True

        # استجابة عادية (بدون payload أو أجزاء خطيرة)
        normal_response = "Hello normal user"
        result = self.scanner._detect_xss(normal_response, xss_payload)
        assert result == False

        # اختبار payload مرمز بالكامل (آمن)
        import html
        encoded_response = f"Hello {html.escape(xss_payload)} user"
        result = self.scanner._detect_xss(encoded_response, xss_payload)
        assert result == False  # payload مرمز بالكامل = آمن

        # اختبار وجود أجزاء خطيرة
        dangerous_response = "Hello <script> user"
        result = self.scanner._detect_xss(dangerous_response, xss_payload)
        assert result == True  # يحتوي على جزء خطير

        print("✅ كشف XSS يعمل بشكل صحيح")

    def test_directory_traversal_detection(self):
        """اختبار كشف Directory Traversal"""
        # محاكاة استجابة تحتوي على محتوى /etc/passwd
        passwd_response = "root:x:0:0:root:/root:/bin/bash\ndaemon:x:1:1:daemon:/usr/sbin:/usr/sbin/nologin"

        result = self.scanner._detect_directory_traversal(passwd_response, "../../../etc/passwd")
        assert result == True

        # استجابة عادية
        normal_response = "File not found"
        result = self.scanner._detect_directory_traversal(normal_response, "../../../etc/passwd")
        assert result == False

        print("✅ كشف Directory Traversal يعمل بشكل صحيح")

    def test_command_injection_detection(self):
        """اختبار كشف Command Injection"""
        # محاكاة استجابة تحتوي على نتيجة أمر ls
        command_response = "total 64\ndrwxr-xr-x 2 <USER> <GROUP> 4096 Jan 1 12:00 documents"

        result = self.scanner._detect_command_injection(command_response, "; ls -la")
        assert result == True

        # استجابة عادية
        normal_response = "Command not found"
        result = self.scanner._detect_command_injection(normal_response, "; ls -la")
        assert result == False

        print("✅ كشف Command Injection يعمل بشكل صحيح")

    def test_url_validation(self):
        """اختبار التحقق من صحة URLs"""
        base_url = "http://example.com"

        # URL صحيح من نفس النطاق
        valid_url = "http://example.com/page1"
        assert self.scanner._is_valid_url(valid_url, base_url) == True

        # URL من نطاق مختلف
        external_url = "http://other-site.com/page"
        assert self.scanner._is_valid_url(external_url, base_url) == False

        # ملف غير مرغوب
        image_url = "http://example.com/image.jpg"
        assert self.scanner._is_valid_url(image_url, base_url) == False

        print("✅ التحقق من صحة URLs يعمل بشكل صحيح")

    def test_evidence_extraction(self):
        """اختبار استخراج الدليل"""
        response_text = "This is a long response with some payload content in the middle and more text after"
        payload = "payload"

        evidence = self.scanner._extract_evidence(response_text, payload)
        assert payload in evidence
        assert len(evidence) <= 200  # يجب أن يكون الدليل مقتطع

        print("✅ استخراج الدليل يعمل بشكل صحيح")

    def test_summary_calculation(self):
        """اختبار حساب ملخص الثغرات"""
        from scanners.web_vulnerability_scanner import Vulnerability

        # إنشاء ثغرات وهمية للاختبار
        vulnerabilities = [
            Vulnerability(
                type=VulnerabilityType.SQL_INJECTION,
                severity=SeverityLevel.HIGH,
                url="http://test.com",
                parameter="id",
                payload="' OR 1=1--",
                evidence="SQL error",
                description="Test SQL injection",
                impact="Data breach",
                remediation="Use prepared statements",
                confidence=0.8
            ),
            Vulnerability(
                type=VulnerabilityType.XSS_REFLECTED,
                severity=SeverityLevel.MEDIUM,
                url="http://test.com",
                parameter="search",
                payload="<script>alert('XSS')</script>",
                evidence="Script executed",
                description="Test XSS",
                impact="Script execution",
                remediation="Encode output",
                confidence=0.9
            )
        ]

        summary = self.scanner._calculate_summary(vulnerabilities)

        assert summary["total"] == 2
        assert summary["high"] == 1
        assert summary["medium"] == 1
        assert summary["critical"] == 0
        assert summary["low"] == 0

        print("✅ حساب ملخص الثغرات يعمل بشكل صحيح")

    def test_csrf_protection_check(self):
        """اختبار فحص حماية CSRF"""
        # HTML مع CSRF token
        html_with_csrf = '''
        <form method="post" action="/submit">
            <input type="hidden" name="_token" value="abc123">
            <input type="text" name="username">
            <input type="submit" value="Submit">
        </form>
        '''

        csrf_vuln = self.scanner._check_csrf_protection("http://test.com", html_with_csrf)
        assert csrf_vuln is None  # لا توجد ثغرة

        # HTML بدون CSRF token
        html_without_csrf = '''
        <form method="post" action="/submit">
            <input type="text" name="username">
            <input type="submit" value="Submit">
        </form>
        '''

        csrf_vuln = self.scanner._check_csrf_protection("http://test.com", html_without_csrf)
        assert csrf_vuln is not None  # توجد ثغرة
        assert csrf_vuln.type == VulnerabilityType.CSRF

        print("✅ فحص حماية CSRF يعمل بشكل صحيح")

if __name__ == "__main__":
    # تشغيل الاختبارات
    test_scanner = TestWebVulnerabilityScanner()
    test_scanner.setup_method()

    print("🔍 بدء اختبارات فاحص ثغرات الويب...")

    try:
        test_scanner.test_sql_payloads_loading()
        test_scanner.test_xss_payloads_loading()
        test_scanner.test_traversal_payloads_loading()
        test_scanner.test_command_payloads_loading()
        test_scanner.test_sql_injection_detection()
        test_scanner.test_xss_detection()
        test_scanner.test_directory_traversal_detection()
        test_scanner.test_command_injection_detection()
        test_scanner.test_url_validation()
        test_scanner.test_evidence_extraction()
        test_scanner.test_summary_calculation()
        test_scanner.test_csrf_protection_check()

        print("\n🎉 جميع اختبارات فاحص الويب نجحت!")

    except Exception as e:
        print(f"❌ فشل في الاختبار: {e}")
        raise
