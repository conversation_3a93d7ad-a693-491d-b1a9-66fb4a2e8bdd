"""
اختبارات وحدة فحص إعدادات الخوادم
"""

import sys
import os

# إضافة مسار التطبيق للاستيراد
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'app'))

from scanners.server_config_scanner import (
    ServerConfigScanner,
    ServerIssueType,
    ServerSeverity,
    SecurityHeaders
)

class TestServerConfigScanner:
    """اختبارات فاحص إعدادات الخوادم"""
    
    def setup_method(self):
        """إعداد الاختبارات"""
        self.scanner = ServerConfigScanner()
    
    def test_scanner_initialization(self):
        """اختبار تهيئة الفاحص"""
        assert self.scanner.timeout == 10
        assert len(self.scanner.common_ports) > 0
        assert 80 in self.scanner.common_ports
        assert 443 in self.scanner.common_ports
        assert len(self.scanner.weak_ssl_versions) > 0
        assert 'SSLv3' in self.scanner.weak_ssl_versions
        print("✅ تم تهيئة فاحص الخوادم بنجاح")
    
    def test_target_parsing(self):
        """اختبار تحليل الهدف"""
        # اختبار domain عادي
        result = self.scanner._parse_target("example.com")
        assert result['hostname'] == "example.com"
        assert result['scheme'] == "https"
        assert result['port'] is None
        
        # اختبار URL كامل
        result = self.scanner._parse_target("http://example.com:8080")
        assert result['hostname'] == "example.com"
        assert result['scheme'] == "http"
        assert result['port'] == 8080
        
        print("✅ تحليل الهدف يعمل بشكل صحيح")
    
    def test_hostname_resolution(self):
        """اختبار تحويل اسم النطاق إلى IP"""
        # اختبار domain معروف
        ip = self.scanner._resolve_hostname("google.com")
        assert ip != "google.com"  # يجب أن يعيد IP وليس نفس الاسم
        
        # اختبار IP مباشر
        ip = self.scanner._resolve_hostname("*******")
        assert ip == "*******"
        
        print("✅ تحويل أسماء النطاقات يعمل بشكل صحيح")
    
    def test_service_detection(self):
        """اختبار تحديد الخدمات"""
        services = self.scanner._detect_services("127.0.0.1", [80, 443, 22])
        
        # فحص أن الخدمات معرفة بشكل صحيح
        assert services.get(80) == "HTTP"
        assert services.get(443) == "HTTPS"
        assert services.get(22) == "SSH"
        
        print("✅ تحديد الخدمات يعمل بشكل صحيح")
    
    def test_ssl_issues_analysis(self):
        """اختبار تحليل مشاكل SSL"""
        from scanners.server_config_scanner import SSLInfo
        from datetime import datetime, timedelta
        
        # إنشاء معلومات SSL وهمية مع مشاكل
        ssl_info = SSLInfo(
            version="TLSv1.0",
            cipher="RC4-MD5",
            certificate_valid=False,
            certificate_expired=True,
            certificate_self_signed=True,
            certificate_issuer="Self-Signed",
            certificate_subject="Test",
            certificate_expiry=datetime.now() - timedelta(days=30),
            supported_protocols=["SSLv3", "TLSv1.0"],
            weak_ciphers=["RC4", "MD5"]
        )
        
        issues = self.scanner._analyze_ssl_issues(ssl_info)
        
        # فحص أن المشاكل تم اكتشافها
        issue_types = [issue.type for issue in issues]
        assert ServerIssueType.SSL_EXPIRED_CERT in issue_types
        assert ServerIssueType.SSL_SELF_SIGNED in issue_types
        assert ServerIssueType.WEAK_SSL_VERSION in issue_types
        assert ServerIssueType.SSL_WEAK_CIPHER in issue_types
        
        print("✅ تحليل مشاكل SSL يعمل بشكل صحيح")
    
    def test_header_issues_analysis(self):
        """اختبار تحليل مشاكل رؤوس الأمان"""
        # رؤوس أمان فارغة (مشاكل متعددة)
        empty_headers = SecurityHeaders()
        issues = self.scanner._analyze_header_issues(empty_headers)
        
        # يجب أن يكتشف عدة رؤوس مفقودة
        assert len(issues) >= 5
        issue_types = [issue.type for issue in issues]
        assert all(issue_type == ServerIssueType.MISSING_SECURITY_HEADER for issue_type in issue_types)
        
        # رؤوس أمان كاملة (لا مشاكل)
        complete_headers = SecurityHeaders(
            strict_transport_security="max-age=31536000",
            content_security_policy="default-src 'self'",
            x_frame_options="DENY",
            x_content_type_options="nosniff",
            x_xss_protection="1; mode=block"
        )
        issues = self.scanner._analyze_header_issues(complete_headers)
        assert len(issues) == 0
        
        print("✅ تحليل مشاكل رؤوس الأمان يعمل بشكل صحيح")
    
    def test_port_issues_analysis(self):
        """اختبار تحليل مشاكل البورتات"""
        # بورتات خطيرة
        dangerous_ports = [21, 23, 25]
        services = {21: "FTP", 23: "Telnet", 25: "SMTP"}
        
        issues = self.scanner._analyze_port_issues(dangerous_ports, services)
        
        # يجب أن يكتشف مشاكل لكل بورت خطير
        assert len(issues) == 3
        issue_types = [issue.type for issue in issues]
        assert all(issue_type == ServerIssueType.INSECURE_SERVICE for issue_type in issue_types)
        
        # بورتات آمنة
        safe_ports = [22, 443]
        safe_services = {22: "SSH", 443: "HTTPS"}
        issues = self.scanner._analyze_port_issues(safe_ports, safe_services)
        assert len(issues) == 0
        
        print("✅ تحليل مشاكل البورتات يعمل بشكل صحيح")
    
    def test_server_info_issues_analysis(self):
        """اختبار تحليل مشاكل معلومات الخادم"""
        # معلومات خادم مكشوفة
        server_info = {
            'server': 'Apache/2.4.41',
            'x_powered_by': 'PHP/7.4.3',
            'x_aspnet_version': '4.0.30319',
            'x_generator': 'WordPress 5.8'
        }
        
        issues = self.scanner._analyze_server_info_issues(server_info)
        
        # يجب أن يكتشف مشاكل لكل معلومة مكشوفة
        assert len(issues) == 4
        issue_types = [issue.type for issue in issues]
        assert all(issue_type == ServerIssueType.INFORMATION_DISCLOSURE for issue_type in issue_types)
        
        # معلومات خادم مخفية
        hidden_info = {'server': '', 'x_powered_by': '', 'status_code': '200'}
        issues = self.scanner._analyze_server_info_issues(hidden_info)
        assert len(issues) == 0
        
        print("✅ تحليل مشاكل معلومات الخادم يعمل بشكل صحيح")
    
    def test_summary_calculation(self):
        """اختبار حساب ملخص المشاكل"""
        from scanners.server_config_scanner import ServerIssue
        
        # إنشاء مشاكل وهمية بمستويات خطورة مختلفة
        issues = [
            ServerIssue(
                type=ServerIssueType.SSL_EXPIRED_CERT,
                severity=ServerSeverity.CRITICAL,
                title="Critical Issue",
                description="Test",
                evidence="Test",
                impact="Test",
                remediation="Test"
            ),
            ServerIssue(
                type=ServerIssueType.WEAK_SSL_VERSION,
                severity=ServerSeverity.HIGH,
                title="High Issue",
                description="Test",
                evidence="Test",
                impact="Test",
                remediation="Test"
            ),
            ServerIssue(
                type=ServerIssueType.MISSING_SECURITY_HEADER,
                severity=ServerSeverity.MEDIUM,
                title="Medium Issue",
                description="Test",
                evidence="Test",
                impact="Test",
                remediation="Test"
            ),
            ServerIssue(
                type=ServerIssueType.INFORMATION_DISCLOSURE,
                severity=ServerSeverity.LOW,
                title="Low Issue",
                description="Test",
                evidence="Test",
                impact="Test",
                remediation="Test"
            )
        ]
        
        summary = self.scanner._calculate_summary(issues)
        
        assert summary["total"] == 4
        assert summary["critical"] == 1
        assert summary["high"] == 1
        assert summary["medium"] == 1
        assert summary["low"] == 1
        assert summary["info"] == 0
        
        print("✅ حساب ملخص المشاكل يعمل بشكل صحيح")
    
    def test_weak_ssl_detection(self):
        """اختبار كشف SSL الضعيف"""
        # فحص البروتوكولات الضعيفة
        assert "SSLv2" in self.scanner.weak_ssl_versions
        assert "SSLv3" in self.scanner.weak_ssl_versions
        assert "TLSv1.0" in self.scanner.weak_ssl_versions
        
        # فحص التشفير الضعيف
        assert "RC4" in self.scanner.weak_ciphers
        assert "DES" in self.scanner.weak_ciphers
        assert "MD5" in self.scanner.weak_ciphers
        
        print("✅ كشف SSL الضعيف يعمل بشكل صحيح")

if __name__ == "__main__":
    # تشغيل الاختبارات
    test_scanner = TestServerConfigScanner()
    test_scanner.setup_method()
    
    print("🔍 بدء اختبارات فاحص إعدادات الخوادم...")
    
    try:
        test_scanner.test_scanner_initialization()
        test_scanner.test_target_parsing()
        test_scanner.test_hostname_resolution()
        test_scanner.test_service_detection()
        test_scanner.test_ssl_issues_analysis()
        test_scanner.test_header_issues_analysis()
        test_scanner.test_port_issues_analysis()
        test_scanner.test_server_info_issues_analysis()
        test_scanner.test_summary_calculation()
        test_scanner.test_weak_ssl_detection()
        
        print("\n🎉 جميع اختبارات فاحص الخوادم نجحت!")
        
    except Exception as e:
        print(f"❌ فشل في الاختبار: {e}")
        raise
